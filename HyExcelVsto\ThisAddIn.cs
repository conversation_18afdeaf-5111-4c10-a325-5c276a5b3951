﻿#region

using ET;
using ET.ETLicense;
using HyExcelVsto.Extensions;
using HyExcelVsto.Module.Common;
using HyExcelVsto.Module.WX;
using Microsoft.Office.Core;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Application = Microsoft.Office.Interop.Excel.Application;

#endregion

namespace HyExcelVsto
{
    /// <summary>
    /// Excel VSTO 插件的主类，负责管理插件的生命周期和全局状态
    /// </summary>
    /// <remarks>
    /// 此类管理：
    /// - Excel应用程序实例
    /// - 全局配置和状态
    /// - 窗体管理
    /// - 事件处理
    /// - 菜单管理
    /// </remarks>
    public partial class ThisAddIn
    {
        #region 其它

        /// <summary>
        /// 删除指定工作簿的单元格候选项记录
        /// </summary>
        /// <param name="workbook">要删除记录的工作簿</param>
        /// <remarks>
        /// 遍历CellCandidateValues字典，删除以指定工作簿名称开头的所有记录。 优化：先收集要删除的键，再统一删除，避免在遍历过程中修改字典。
        /// 建议优化命名：删除单元格候选项记录 → RemoveCellCandidateRecords
        /// </remarks>
        private void 删除单元格候选项记录(Workbook workbook)
        {
            if (workbook?.Name == null)
            {
                ETLogManager.Warning("工作簿或工作簿名称为空，跳过删除单元格候选项记录");
                return;
            }

            try
            {
                // 先收集要删除的键，避免在遍历过程中修改字典
                List<string> keysToRemove = new List<string>();

                foreach (KeyValuePair<string, List<string>> candidateEntry in GlobalSettings.CellCandidateValues)
                {
                    if (candidateEntry.Key.StartsWith(workbook.Name, StringComparison.OrdinalIgnoreCase))
                    {
                        keysToRemove.Add(candidateEntry.Key);
                    }
                }

                // 统一删除收集到的键
                foreach (string key in keysToRemove)
                {
                    GlobalSettings.CellCandidateValues.Remove(key);
                }

                // 已删除单元格候选项记录（不输出日志）
            }
            catch (InvalidOperationException ex)
            {
                // 字典正在被其他线程修改
                ETLogManager.Warning($"删除单元格候选项记录时字典正在被修改: {ex.Message}");
            }
            catch (Exception ex)
            {
                // 其他未预期的异常
                ETLogManager.Error($"删除工作簿 '{workbook.Name}' 的单元格候选项记录时发生错误", ex);
            }
        }

        #endregion

        #region 常量定义

        /// <summary>
        /// 应用程序常量
        /// </summary>
        private const int DEFAULT_MENU_REBUILD_DELAY_MS = 100;

        private const int FILE_LOCK_TIMER_INTERVAL_MS = 3 * 60 * 1000; // 3分钟
        private const int DEFAULT_TASKPANE_WIDTH = 250;
        private const int MAX_CLIPBOARD_TEXT_LENGTH = 200;
        private const int MAX_TAG_COUNT_LIMIT = 100;
        private const int DEFAULT_COMMUNICATION_PORT = 8888;
        #endregion

        #region 定义变量

        public static Application ExcelApplication;
        public static ThisAddIn ThisAddInInstance = Globals.ThisAddIn;
        public static ETIniFile ConfigurationSettings = new(ETConfig.GetConfigDirectory("config.ini"));

        /// <summary>
        /// 授权控制器，用于验证应用权限 注意：此属性已迁移到HyLicenseManager，建议使用HyLicenseManager.LicenseController
        /// </summary>
        public static ETLicenseController LicenseController => HyLicenseManager.LicenseController;

        public static FileInfo TemplateFile;

        //菜单相关
        private static readonly object _menuLock = new();

        private static MenuManager _menuManagerInstance;
        public static readonly List<CommandBarButton> CustomMenuButtons = [];
        private static readonly HashSet<string> RegisteredCommandButtons = [];

        /// <summary>
        /// 获取菜单管理器实例，确保线程安全和单例模式
        /// </summary>
        public static MenuManager MenuManagerInstance
        {
            get
            {
                if (_menuManagerInstance == null)
                {
                    lock (_menuLock)
                    {
                        if (_menuManagerInstance == null && ExcelApplication != null)
                        {
                            _menuManagerInstance = new MenuManager(ExcelApplication, CustomMenuButtons, RegisteredCommandButtons);
                        }
                    }
                }
                return _menuManagerInstance;
            }
        }

        // 窗体相关 - 线程安全集合
        public static frmTopMostForm TopMostForm = new();

        private static readonly object _openFormHandlesLock = new();
        private static readonly HashSet<IntPtr> _openFormHandles = [];
        public static frmLongStringDisplayForm LongStringDisplayForm = new();
        public static frmDropdownInputForm DropdownInputForm = new();
        public static frmCrosshairOverlayForm CrosshairOverlayForm = new();

        /// <summary>
        /// 线程安全的打开窗体句柄集合
        /// </summary>
        public static HashSet<IntPtr> OpenFormHandles
        {
            get
            {
                lock (_openFormHandlesLock)
                {
                    return new HashSet<IntPtr>(_openFormHandles);
                }
            }
        }

        /// <summary>
        /// 添加窗体句柄到集合
        /// </summary>
        /// <param name="handle">窗体句柄</param>
        public static void AddFormHandle(IntPtr handle)
        {
            lock (_openFormHandlesLock)
            {
                _openFormHandles.Add(handle);
            }
        }

        /// <summary>
        /// 从集合中移除窗体句柄
        /// </summary>
        /// <param name="handle">窗体句柄</param>
        public static void RemoveFormHandle(IntPtr handle)
        {
            lock (_openFormHandlesLock)
            {
                _openFormHandles.Remove(handle);
            }
        }

        public static bool IsExcelActive = true;

        // 文件锁定相关 - 线程安全集合
        private static readonly object _lockedFilesLock = new();

        private static readonly HashSet<string> _lockedFiles = [];
        internal static readonly System.Windows.Forms.Timer FileMonitorTimer = new();

        /// <summary>
        /// 线程安全的锁定文件集合
        /// </summary>
        private static HashSet<string> LockedFiles
        {
            get
            {
                lock (_lockedFilesLock)
                {
                    return new HashSet<string>(_lockedFiles);
                }
            }
        }

        /// <summary>
        /// Event handling flags - 建议添加IsPrefix
        /// </summary>
        /// <remarks>Event handling flags - 建议添加IsPrefix</remarks>
        public static bool IsSheetSelectionChangeHandleEnable = true;

        public static bool IsSheetActivateHandleEnable = true;
        public static bool IsSheetChangeHandleEnable = true;
        public static bool IsSheetDeactivateHandleEnable = true;
        public static bool IsWorkbookActivateHandleEnable = true;
        public static bool IsWorkbookDeactivateHandleEnable = true;
        public static bool IsWorkbookOpenHandleEnable = true;
        public static bool IsWindowActivateHandleEnable = true;
        public static bool IsWindowDeactivateHandleEnable = true;
        public static bool IsWindowResizeHandleEnable = true;
        #endregion 定义变量

        #region VSTO 初始化
        //IntPtr excelHwnd;

        /// <summary>
        /// VSTO插件启动时的初始化方法
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void ThisAddIn_Startup(object sender, EventArgs e)
        {
            // 开始追踪程序集加载，特别是Accessibility程序集
            AssemblyLoadTracker.StartTracking();

            try
            {
                Stopwatch startupStopwatch = System.Diagnostics.Stopwatch.StartNew();
                ETLogManager.Info("开始VSTO插件启动流程");

                // 记录当前已加载的程序集（不输出详细信息）
                AssemblyLoadTracker.CheckCurrentAssemblies();

                // 阶段1：核心同步初始化（必须完成的关键组件）
                InitializeGlobalInstances();
                InitializeHelpForms();

                // 阶段2：异步初始化（非阻塞，后台进行）
                Task.Run(async () =>
                {
                    try
                    {
                        await InitializeSecondaryComponentsAsync().ConfigureAwait(false);
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error("异步组件初始化失败", ex);
                    }
                });

                // 阶段3：菜单系统初始化（在启动时直接初始化）
                Task.Run(() =>
                {
                    try
                    {
                        EnsureMenuInitialized();
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error("菜单系统启动时初始化失败", ex);
                    }
                });

                startupStopwatch.Stop();
                ETLogManager.Info("VSTO插件启动流程完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("启动时发生错误", ex);
            }
        }

        /// <summary>
        /// 初始化全局实例
        /// </summary>
        private void InitializeGlobalInstances()
        {
            ThisAddInInstance = Globals.ThisAddIn;
            GlobalSettings.IsWps = ETExcelExtensions.IsWPS();
            GlobalSettings.EnableHelpForm = true;
            OpenFormHandles.Add(new IntPtr(Globals.ThisAddIn.Application.Hwnd));
        }

        /// <summary>
        /// 菜单系统是否已初始化
        /// </summary>
        internal bool _menuInitialized = false;

        internal readonly object _menuInitLock = new object();

        /// <summary>
        /// 确保菜单系统已初始化
        /// </summary>
        /// <remarks>此方法确保菜单系统只初始化一次，支持在启动时或按需调用。 使用双重检查锁定模式确保线程安全。</remarks>
        public void EnsureMenuInitialized()
        {
            if (_menuInitialized) return;

            lock (_menuInitLock)
            {
                if (_menuInitialized) return;

                Stopwatch menuStopwatch = System.Diagnostics.Stopwatch.StartNew();

                try
                {
                    // 获取菜单管理器实例
                    MenuManager menuManager = MenuManagerInstance;

                    // 同步初始化菜单（避免异步复杂性）
                    menuManager.RebuildAllMenus();

                    _menuInitialized = true;
                    menuStopwatch.Stop();
                }
                catch (Exception ex)
                {
                    ETLogManager.Error("菜单系统初始化失败", ex);
                    throw;
                }
            }
        }

        /// <summary>
        /// 初始化辅助窗体
        /// </summary>
        private void InitializeHelpForms()
        {
            if (GlobalSettings.EnableHelpForm)
            {
                InitializeTopForm();
                InitializeOverlayForms();
            }
            else
            {
                DisableHelpFormFeatures();
            }

            if (GlobalSettings.IsWps)
            {
                DisableWpsIncompatibleFeatures();
            }
        }

        /// <summary>
        /// 初始化顶层窗体
        /// </summary>
        private void InitializeTopForm()
        {
            ETForm.NoActivateStyle(DropdownInputForm);
            TopMostForm.Visible = false;
            TopMostForm.Top = 10000;
            TopMostForm.Show();
            OpenFormHandles.Add(TopMostForm.Handle);
            TopMostForm.Start();
        }

        /// <summary>
        /// 初始化覆盖层窗体
        /// </summary>
        private void InitializeOverlayForms()
        {
            if (Globals.Ribbons.hyRibbon.checkBoxHorizontalHighlight.Checked ||
                Globals.Ribbons.hyRibbon.checkBoxVerticalHighlight.Checked)
            {
                LoadHighlightOverlayForm();
            }
            if (Globals.Ribbons.hyRibbon.checkBox叠加显示辅助.Checked)
            {
                LoadHelpForm();
            }
        }

        /// <summary>
        /// 禁用辅助窗体功能
        /// </summary>
        private void DisableHelpFormFeatures()
        {
            Globals.Ribbons.hyRibbon.checkBoxHorizontalHighlight.Visible = false;
            Globals.Ribbons.hyRibbon.checkBox叠加显示辅助.Visible = false;
            Globals.Ribbons.hyRibbon.checkBoxVerticalHighlight.Visible = false;
        }

        /// <summary>
        /// 禁用WPS不兼容的功能
        /// </summary>
        private void DisableWpsIncompatibleFeatures()
        {
            Globals.Ribbons.hyRibbon.checkBoxHorizontalHighlight.Checked = false;
            Globals.Ribbons.hyRibbon.checkBoxHorizontalHighlight.Visible = false;
            Globals.Ribbons.hyRibbon.checkBoxVerticalHighlight.Checked = false;
            Globals.Ribbons.hyRibbon.checkBoxVerticalHighlight.Visible = false;
        }

        /// <summary>
        /// 初始化应用程序设置
        /// </summary>
        private void InitializeSettings()
        {
            // 初始化全局设置
            GlobalSettings.IsWps = ETExcelExtensions.IsWPS();
            GlobalSettings.EnableHelpForm = true;
            GlobalSettings.AutoBackupPath = ConfigurationSettings.GetValue("file", "backupfolder");

            InitializeRibbonCheckboxStates();
        }

        /// <summary>
        /// 初始化Ribbon复选框状态
        /// </summary>
        private void InitializeRibbonCheckboxStates()
        {
            if (GlobalSettings.EnableHelpForm)
            {
                // 使用 ETForm.HyBindWindowsFormControl 方法绑定控件
                ETForm.BindWindowsFormControl(Globals.Ribbons.hyRibbon.checkBox监控剪贴板, ConfigurationSettings, "设置", "bool监控剪贴板");
                ETForm.BindWindowsFormControl(Globals.Ribbons.hyRibbon.checkBoxHorizontalHighlight, ConfigurationSettings, "设置", "bool水平高亮行列");
                ETForm.BindWindowsFormControl(Globals.Ribbons.hyRibbon.checkBoxVerticalHighlight, ConfigurationSettings, "设置", "bool垂直高亮行列");
                ETForm.BindWindowsFormControl(Globals.Ribbons.hyRibbon.checkBox叠加显示辅助, ConfigurationSettings, "设置", "bool叠加显示辅助");
            }
            ETForm.BindWindowsFormControl(Globals.Ribbons.hyRibbon.checkBoxStockHelper, ConfigurationSettings, "设置", "stockHelper");
            GlobalSettings.IsStockDataHelperEnabled = Globals.Ribbons.hyRibbon.checkBoxStockHelper.Checked;
        }

        /// <summary>
        /// 异步初始化次要组件
        /// </summary>
        private async Task InitializeSecondaryComponentsAsync()
        {
            Stopwatch secondaryStopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                // 异步初始化设置
                await Task.Run(() =>
                {
                    try
                    {
                        InitializeSettings();
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error("设置初始化失败", ex);
                    }
                }).ConfigureAwait(false);

                // 异步初始化通信服务
                await Task.Run(() =>
                {
                    try
                    {
                        //【优化】先不要初始化通信服务，后期改为按需初始化
                        //InitializeCommunicationService();
                        //ETLogManager.Debug("通信服务初始化完成");
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Error("通信服务初始化失败", ex);
                    }
                }).ConfigureAwait(false);

                secondaryStopwatch.Stop();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("次要组件异步初始化失败", ex);
            }
        }

        private void ThisAddIn_Shutdown(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info("开始VSTO插件关闭流程");

                // 停止和释放定时器
                try
                {
                    if (FileMonitorTimer != null)
                    {
                        FileMonitorTimer.Stop();
                        FileMonitorTimer.Tick -= LockFileTimer_Tick;
                        FileMonitorTimer.Dispose();
                    }
                }
                catch (Exception ex)
                {
                    ETLogManager.Warning($"释放文件监控定时器时出错: {ex.Message}");
                }

                // 取消事件订阅
                try
                {
                    UnregisterEventHandlers();
                }
                catch (Exception ex)
                {
                    ETLogManager.Warning($"取消事件订阅时出错: {ex.Message}");
                }

                // 清理窗体资源
                try
                {
                    CleanupFormResources();
                }
                catch (Exception ex)
                {
                    ETLogManager.Warning($"清理窗体资源时出错: {ex.Message}");
                }

                // 优雅地关闭通信服务
                try
                {
                    if (CommunicationService != null)
                    {
                        CommunicationService.Stop();
                        CommunicationService = null;
                    }
                }
                catch (Exception ex)
                {
                    ETLogManager.Warning($"关闭通信服务时出错: {ex.Message}");
                }

                // 停止程序集追踪并保存日志
                try
                {
                    AssemblyLoadTracker.StopTracking();
                    string logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs", $"AssemblyTrace_{DateTime.Now:yyyyMMdd_HHmmss}.txt");
                    Directory.CreateDirectory(Path.GetDirectoryName(logPath));
                    AssemblyLoadTracker.SaveLogToFile(logPath);
                    ETLogManager.Info($"程序集追踪日志已保存到: {logPath}");
                }
                catch (Exception ex)
                {
                    ETLogManager.Warning($"保存程序集追踪日志时出错: {ex.Message}");
                }

                ETLogManager.Info("VSTO插件关闭流程完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("插件关闭过程中发生严重错误", ex);
            }
        }

        /// <summary>
        /// 取消注册所有事件处理程序
        /// </summary>
        private void UnregisterEventHandlers()
        {
            try
            {
                // 取消启动和关闭事件
                Startup -= ThisAddIn_Startup;
                Shutdown -= ThisAddIn_Shutdown;

                // 取消窗口事件
                if (Application != null)
                {
                    Application.WindowActivate -= App_WindowActivate;
                    Application.WindowDeactivate -= App_WindowDeactivate;
                    Application.WindowResize -= Application_WindowResize;

                    // 取消工作簿事件
                    Application.WorkbookBeforeSave -= App_WorkbookBeforeSave;
                    Application.WorkbookBeforeClose -= App_WorkbookBeforeClose;
                    Application.WorkbookBeforePrint -= App_WorkbookBeforePrint;
                    Application.WorkbookActivate -= App_WorkbookActivate;
                    Application.WorkbookDeactivate -= App_WorkbookDeactivate;
                    Application.WorkbookOpen -= App_WorkbookOpen;

                    // 取消工作表事件
                    Application.SheetActivate -= App_SheetActivate;
                    Application.SheetChange -= App_SheetChange;
                    Application.SheetDeactivate -= App_SheetDeactivate;

                    // 取消单元格事件
                    Application.SheetSelectionChange -= App_SheetSelectionChange;
                    Application.SheetBeforeRightClick -= App_SheetBeforeRightClick;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("取消事件订阅时发生错误", ex);
            }
        }

        /// <summary>
        /// 清理窗体资源
        /// </summary>
        private void CleanupFormResources()
        {
            try
            {
                // 清理任务窗格
                if (_taskPaneManager != null)
                {
                    // TaskPaneManager 应该有自己的清理方法
                }

                // 清理窗体管理器
                if (_formManager != null)
                {
                    // FormManager 应该有自己的清理方法
                }

                // 清理打开的窗体句柄
                lock (_openFormHandlesLock)
                {
                    _openFormHandles.Clear();
                }

                // 清理锁定文件列表
                lock (_lockedFilesLock)
                {
                    _lockedFiles.Clear();
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("清理窗体资源时发生错误", ex);
            }
        }

        /// <summary>
        /// VSTO插件的内部启动方法
        /// </summary>
        /// <remarks>
        /// 负责：
        /// - 初始化Excel应用程序实例
        /// - 设置各个类的Excel应用程序引用
        /// - 注册事件处理程序
        /// - 加载配置和授权信息
        /// - 初始化定时器和设置
        /// </remarks>
        private void InternalStartup()
        {
            InitializeExcelApplication();
            RegisterEventHandlers();
            LoadConfigurationAndAuthorization();
            HyLicenseManager.InitializeTimerAndSettings();
        }

        /// <summary>
        /// 初始化Excel应用程序实例和相关引用
        /// </summary>
        private void InitializeExcelApplication()
        {
            try
            {
                // 开始初始化Excel应用程序实例
                ExcelApplication = Globals.ThisAddIn.Application;
                if (ExcelApplication == null)
                {
                    throw new ETException("Excel应用程序实例为空", "应用程序初始化");
                }

                // 给各个类赋值
                ETExcelExtensions.App = ExcelApplication;
                HyRibbonClass.xlApp = ExcelApplication;
                HyFunctions.XlApp = ExcelApplication;
                ETForm.SaveExcelAppHwnd();

                ETLogManager.Info("成功初始化Excel应用程序实例");
            }
            catch (Exception ex)
            {
                throw new ETException("初始化Excel应用程序失败", "应用程序初始化", ex);
            }
        }

        /// <summary>
        /// 注册所有事件处理程序
        /// </summary>
        private void RegisterEventHandlers()
        {
            RegisterStartupShutdownHandlers();
            RegisterWindowHandlers();
            RegisterWorkbookHandlers();
            RegisterWorksheetHandlers();
            RegisterCellHandlers();
        }

        /// <summary>
        /// 注册启动和关闭事件处理程序
        /// </summary>
        private void RegisterStartupShutdownHandlers()
        {
            Startup += ThisAddIn_Startup;
            Shutdown += ThisAddIn_Shutdown;
        }

        /// <summary>
        /// 注册窗口相关事件处理程序
        /// </summary>
        private void RegisterWindowHandlers()
        {
            Application.WindowActivate += App_WindowActivate;
            Application.WindowDeactivate += App_WindowDeactivate;
            Application.WindowResize += Application_WindowResize;
        }

        /// <summary>
        /// 注册工作簿相关事件处理程序
        /// </summary>
        private void RegisterWorkbookHandlers()
        {
            Application.WorkbookBeforeSave += App_WorkbookBeforeSave;
            Application.WorkbookBeforeClose += App_WorkbookBeforeClose;
            Application.WorkbookBeforePrint += App_WorkbookBeforePrint;
            Application.WorkbookActivate += App_WorkbookActivate;
            Application.WorkbookDeactivate += App_WorkbookDeactivate;
            Application.WorkbookOpen += App_WorkbookOpen;
        }

        /// <summary>
        /// 注册工作表相关事件处理程序
        /// </summary>
        private void RegisterWorksheetHandlers()
        {
            Application.SheetActivate += App_SheetActivate;
            Application.SheetChange += App_SheetChange;
            Application.SheetDeactivate += App_SheetDeactivate;
        }

        /// <summary>
        /// 注册单元格相关事件处理程序
        /// </summary>
        private void RegisterCellHandlers()
        {
            Application.SheetSelectionChange += App_SheetSelectionChange;
            Application.SheetBeforeRightClick += App_SheetBeforeRightClick;
        }

        /// <summary>
        /// 加载配置和授权信息
        /// </summary>
        private void LoadConfigurationAndAuthorization()
        {
            try
            {
                // 开始加载配置和授权信息

                if (string.IsNullOrEmpty(GlobalSettings.AutoBackupPath))
                {
                    ETLogManager.Info("自动备份路径未配置");
                }

                // 初始化授权控制器
                HyLicenseManager.InitializeLicenseController();

                // 检查权限，验证功能
                HyLicenseManager.InitializeAuthorization();

                string templatePath = ETConfig.GetConfigDirectory(@"hyExcelDnaData.xlsx", ".template");
                TemplateFile = new FileInfo(templatePath);
                if (!TemplateFile.Exists)
                {
                    ETLogManager.Info($"模板文件不存在: {templatePath}");
                }

                ETLogManager.Info("成功加载配置和授权信息");
            }
            catch (Exception ex)
            {
                throw new ETException("加载配置或授权信息失败", "配置加载", ex);
            }
        }

        #region 定时设置为只读

        /// <summary>
        /// 添加文件到只读监控列表
        /// </summary>
        /// <param name="filePath">要监控的文件路径</param>
        /// <remarks>将文件添加到FileLockHashSet中，并启动3分钟定时器。 定时器到期后，将把所有监控列表中的文件设置为只读。 优化：使用线程安全的集合操作和常量定义</remarks>
        public static void AddToLockFile(string filePath)
        {
            if (string.IsNullOrWhiteSpace(filePath))
            {
                ETLogManager.Warning("文件路径为空，跳过添加到锁定文件列表");
                return;
            }

            try
            {
                lock (_lockedFilesLock)
                {
                    _lockedFiles.Add(filePath);
                }

                FileMonitorTimer.Interval = FILE_LOCK_TIMER_INTERVAL_MS;
                FileMonitorTimer.Enabled = true;

                // 已添加文件到锁定监控列表
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"添加文件到锁定列表时发生错误: {filePath}", ex);
            }
        }

        /// <summary>
        /// 定时器触发事件处理程序
        /// </summary>
        /// <remarks>将FileLockHashSet中的所有文件设置为只读，然后清空列表并停止定时器。 优化：改进命名规范和错误处理</remarks>
        internal void LockFileTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                HashSet<string> filesToLock;
                lock (_lockedFilesLock)
                {
                    filesToLock = new HashSet<string>(_lockedFiles);
                    _lockedFiles.Clear();
                }

                int lockedCount = 0;
                foreach (string filePath in filesToLock)
                {
                    try
                    {
                        if (File.Exists(filePath))
                        {
                            File.SetAttributes(filePath, FileAttributes.ReadOnly);
                            lockedCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Warning($"设置文件只读属性失败: {filePath}, 错误: {ex.Message}");
                    }
                }

                FileMonitorTimer.Enabled = false;

                if (lockedCount > 0)
                {
                    ETLogManager.Info($"已将 {lockedCount} 个文件设置为只读");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("定时器处理锁定文件时发生错误", ex);
                FileMonitorTimer.Enabled = false;
            }
        }

        #endregion

        #endregion VSTO 生成的代码

        #region App窗体事件

        private void Application_WindowResize(Workbook workbook, Window window)
        {
            if (!IsWindowResizeHandleEnable)
                return;

            HideToolsWindows();

            // 重新加载十字光标
            ReLoadHighlightOverlayForm();

            // 窗口大小改变后重新验证TopForm父子关系
            try
            {
                TopMostForm?.ForceRestoreParentRelation();
                ETLogManager.Info("Application_WindowResize: 已重新验证TopForm父子关系");
            }
            catch (Exception ex)
            {
                ETLogManager.Warning($"Application_WindowResize: 重新验证TopForm关系失败 - {ex.Message}");
            }
        }

        private void App_WindowActivate(Workbook workbook, Window window)
        {
            try
            {
                // 窗口激活时重新启动TopForm管理
                TopMostForm?.Start();

                // 额外验证父子关系
                if (TopMostForm != null && GlobalSettings.EnableHelpForm)
                {
                    // 延迟一小段时间后再次验证，确保窗口状态稳定
                    System.Threading.Tasks.Task.Delay(500).ContinueWith(_ =>
                    {
                        try
                        {
                            TopMostForm.ForceRestoreParentRelation();
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Warning($"App_WindowActivate: 延迟验证TopForm关系失败 - {ex.Message}");
                        }
                    });
                }

                ETLogManager.Info("App_WindowActivate: Excel窗口激活处理完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Warning($"App_WindowActivate: 处理窗口激活事件失败 - {ex.Message}");
            }

            if (!IsWindowActivateHandleEnable)
                return;
        }

        private void App_WindowDeactivate(Workbook workbook, Window window)
        {
            //Debug.Print("App_WindowDeactivate");
            if (!IsWindowDeactivateHandleEnable)
                return;

            HideToolsWindows();
        }

        #endregion App窗体事件

        #region App工作簿事件

        private void App_WorkbookBeforeSave(Workbook workbook, bool saveAsUi, ref bool cancel)
        {
            //Debug.Print("App_WorkbookBeforeSave");
        }

        private void App_WorkbookBeforeClose(Workbook workbook, ref bool cancel)
        {
            try
            {
                TopMostForm.Stop();

                // 清理即将关闭的工作簿的TaskPane
                if (_taskPaneManager != null && workbook != null)
                {
                    _taskPaneManager.CleanupWorkbookTaskPanes(workbook.Name);
                }

                删除单元格候选项记录(workbook);
            }
            catch { /* 忽略清理过程中的错误 */ }
        }

        private void App_WorkbookBeforePrint(Workbook workbook, ref bool cancel)
        {
            //Debug.Print("App_WorkbookBeforePrint");
        }

        private void App_WorkbookActivate(Workbook workbook)
        {
            try
            {
                // 使用线程安全的方法添加窗体句柄
                AddFormHandle(new IntPtr(Globals.ThisAddIn.Application.Hwnd));

                // 工作簿激活时重新启动TopForm管理
                TopMostForm?.Start();

                // 验证并修复可能的窗体层级问题
                if (GlobalSettings.EnableHelpForm && TopMostForm != null)
                {
                    // 工作簿切换可能导致窗口句柄变化，强制验证关系
                    System.Threading.Tasks.Task.Run(() =>
                    {
                        try
                        {
                            System.Threading.Thread.Sleep(300); // 等待工作簿激活完成
                            TopMostForm.ForceRestoreParentRelation();
                            ETLogManager.Info("App_WorkbookActivate: TopForm关系验证完成");
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Warning($"App_WorkbookActivate: TopForm关系验证失败 - {ex.Message}");
                        }
                    });
                }

                if (!IsWorkbookActivateHandleEnable)
                    return;

                // 工作簿已激活
                ETLogManager.Info($"App_WorkbookActivate: 工作簿 '{workbook?.Name}' 激活处理完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Warning($"处理工作簿激活事件时出错: {ex.Message}");
            }
        }

        private void App_WorkbookDeactivate(Workbook workbook)
        {
            //Debug.Print("App_WorkbookDeactivate");
            if (!IsWorkbookDeactivateHandleEnable)
                return;

            HideToolsWindows();
        }

        private void App_WorkbookOpen(Workbook workbook)
        {
            try
            {
                ETLogManager.Info($"App_WorkbookOpen: 工作簿 '{workbook?.Name}' 打开事件触发");

                // 工作簿打开时重新启动TopForm管理
                TopMostForm?.Start();

                // 新工作簿打开可能影响窗口层级，延迟验证关系
                if (GlobalSettings.EnableHelpForm && TopMostForm != null)
                {
                    System.Threading.Tasks.Task.Run(async () =>
                    {
                        try
                        {
                            await System.Threading.Tasks.Task.Delay(800); // 等待工作簿完全加载
                            TopMostForm.ForceRestoreParentRelation();
                            ETLogManager.Info("App_WorkbookOpen: TopForm关系验证完成");
                        }
                        catch (Exception ex)
                        {
                            ETLogManager.Warning($"App_WorkbookOpen: TopForm关系验证失败 - {ex.Message}");
                        }
                    });
                }

                if (!IsWorkbookOpenHandleEnable)
                    return;

                ETLogManager.Info($"App_WorkbookOpen: 工作簿 '{workbook?.Name}' 打开处理完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Warning($"App_WorkbookOpen: 处理工作簿打开事件失败 - {ex.Message}");
            }
        }

        #endregion App工作簿事件

        #region App工作表事件

        private void App_SheetActivate(object worksheet)
        {
            //Debug.Print("App_SheetActivate");
            TopMostForm.Start();
            if (!IsSheetActivateHandleEnable)
                return;
        }

        private void App_SheetChange(object worksheet, Range target)
        {
            //Debug.Print("App_SheetChange");
            if (!IsSheetChangeHandleEnable)
                return;

            HideToolsWindows();
        }

        private void App_SheetDeactivate(object worksheet)
        {
            //Debug.Print("App_SheetDeactivate");
            if (!IsSheetDeactivateHandleEnable)
                return;

            HideToolsWindows();
        }

        private void App_SheetBeforeDelete(object worksheet)
        {
            HideToolsWindows();
            //Debug.Print("App_SheetBeforeDelete");
        }

        #endregion App工作表事件

        #region App单元格事件

        /// <summary>
        /// 处理工作表选择变更事件
        /// </summary>
        /// <param name="worksheet">工作表对象</param>
        /// <param name="target">目标范围</param>
        /// <remarks>优化：将过长的方法拆分为多个职责单一的方法</remarks>
        public void App_SheetSelectionChange(object worksheet, Range target)
        {
            try
            {
                if (!IsSheetSelectionChangeHandleEnable)
                    return;

                ExcelApplication = Globals.ThisAddIn.Application;
                Range selectionRange = ExcelApplication?.Selection;
                if (selectionRange == null)
                    return;

                Range activeCell = selectionRange.Cells[1, 1];

                // 处理UI更新
                HandleUIUpdates(selectionRange, activeCell, target);
            }
            catch (Exception ex)
            {
                ETLogManager.Error("处理工作表选择变更事件时发生错误", ex);
            }
        }

        /// <summary>
        /// 处理UI更新
        /// </summary>
        /// <param name="selectionRange">选择范围</param>
        /// <param name="activeCell">活动单元格</param>
        /// <param name="target">目标范围</param>
        private void HandleUIUpdates(Range selectionRange, Range activeCell, Range target)
        {
            try
            {
                // 记录当前Excel应用窗口句柄
                ETForm.SaveExcelAppHwnd();

                // 获得坐标信息，供后续使用
                WinAPI.RECT rangeRect = ETForm.RangeRect(ExcelApplication, selectionRange);
                WinAPI.RECT areaRect = ETForm.WorksheetWorkingAreaRect(ExcelApplication);

                // 重新加载十字光标
                ReLoadHighlightOverlayForm(areaRect, rangeRect);

                // 处理辅助窗体显示
                HandleHelperFormsDisplay(selectionRange, activeCell, rangeRect, areaRect);

                // 通知相关组件
                NotifyComponentsSelectionChange(target, activeCell);
            }
            catch (Exception ex)
            {
                ETLogManager.Error("处理UI更新时发生错误", ex);
            }
        }

        /// <summary>
        /// 处理辅助窗体显示
        /// </summary>
        /// <param name="selectionRange">选择范围</param>
        /// <param name="activeCell">活动单元格</param>
        /// <param name="rangeRect">范围矩形</param>
        /// <param name="areaRect">区域矩形</param>
        private void HandleHelperFormsDisplay(Range selectionRange, Range activeCell, WinAPI.RECT rangeRect, WinAPI.RECT areaRect)
        {
            if (!Globals.Ribbons.hyRibbon.checkBox叠加显示辅助.Checked)
                return;

            try
            {
                // 获取选中单元格数量
                int selectionCellsCount = GetSelectionCellsCount(selectionRange);

                // 控制输入辅助窗和文本显示辅助框的显示与隐藏
                if (selectionCellsCount == 1)
                {
                    HandleSingleCellSelection(activeCell, rangeRect, areaRect);
                }
                else
                {
                    HideHelperForms();
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("处理辅助窗体显示时发生错误", ex);
            }
        }

        /// <summary>
        /// 获取选择单元格数量
        /// </summary>
        /// <param name="selectionRange">选择范围</param>
        /// <returns>单元格数量</returns>
        private int GetSelectionCellsCount(Range selectionRange)
        {
            try
            {
                return selectionRange.Cells.Count;
            }
            catch
            {
                return int.MaxValue;
            }
        }

        /// <summary>
        /// 处理单个单元格选择
        /// </summary>
        /// <param name="activeCell">活动单元格</param>
        /// <param name="rangeRect">范围矩形</param>
        /// <param name="areaRect">区域矩形</param>
        private void HandleSingleCellSelection(Range activeCell, WinAPI.RECT rangeRect, WinAPI.RECT areaRect)
        {
            try
            {
                string helperMod = string.IsNullOrWhiteSpace(activeCell.Validation.ErrorTitle)
                    ? string.Empty
                    : activeCell.Validation.ErrorTitle.Trim();

                if (!(activeCell.Value is DateTime))
                {
                    int inputHelperShowed = DropdownInputForm.ChangeRange(activeCell, rangeRect);
                    LongStringDisplayForm.ChangeText(activeCell, rangeRect, areaRect, inputHelperShowed, helperMod);
                }
                else
                {
                    HideHelperForms();
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("处理单个单元格选择时发生错误", ex);
            }
        }

        /// <summary>
        /// 隐藏辅助窗体
        /// </summary>
        private void HideHelperForms()
        {
            try
            {
                LongStringDisplayForm?.HideWindow();
                DropdownInputForm?.HideWindow();
            }
            catch (Exception ex)
            {
                ETLogManager.Warning($"隐藏辅助窗体时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 通知相关组件选择变更
        /// </summary>
        /// <param name="target">目标范围</param>
        /// <param name="activeCell">活动单元格</param>
        private void NotifyComponentsSelectionChange(Range target, Range activeCell)
        {
            try
            {
                bool containsValidation = activeCell.RangeIsContainValidation();

                // 通知所有已打开的窗体
                NotifyFormsSelectionChange(target, containsValidation ? "yes" : null);

                // 通知所有TaskPane
                _taskPaneManager?.NotifyTaskPanesSelectionChange(target);
            }
            catch (Exception ex)
            {
                ETLogManager.Error("通知组件选择变更时发生错误", ex);
            }
        }

        // 原函数名：App_SheetBeforeRightClick
        private void App_SheetBeforeRightClick(object sh, Range target, ref bool cancel)
        {
            //Debug.Print("App_SheetBeforeRightClick");

            try
            {
                // 监控剪贴板内并根据监控状态显示/隐藏无格式复制菜单项
                bool clipMonitorEnabled = Globals.Ribbons.hyRibbon.checkBox监控剪贴板.Checked;
                ETForm.ShowHide菜单子项(Application.CommandBars["cell"].Controls, new[] { "无格式复制" }, clipMonitorEnabled, true);

                if (clipMonitorEnabled)
                {
                    // 获取剪贴板文本内容，使用常量定义长度限制
                    string clipboardText = ETString.GetClipboardText(MAX_CLIPBOARD_TEXT_LENGTH);

                    // 根据剪贴板内容判断是否显示粘贴工参菜单项
                    bool is参数表格 = HyFunctions.Check是否工参表格(clipboardText);
                    if (is参数表格)
                        ETForm.ShowHide菜单子项(Application.CommandBars["cell"].Controls, new[] { "粘贴工参" }, is参数表格, true);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Warning($"处理右键菜单时出错: {ex.Message}");
            }
        }

        #endregion App单元格事件

        #region 窗体相关
        private static readonly FormManager _formManager = new();

        //窗口管理
        public static Dictionary<string, Form> OpenFormNames => _formManager._openForms; // 为了保持向后兼容

        /// <summary>
        /// 通过窗体标题查找窗体实例
        /// </summary>
        public static Form FindOpenForm(string formText)
        { return _formManager.FindOpenForm(formText); }

        /// <summary>
        /// 打开一个窗体
        /// </summary>
        public static void OpenForm(
            Form form,
            XlFormPosition position = XlFormPosition.Center,
            bool singleInstance = true)
        { _formManager.OpenForm(form, position, singleInstance); }

        /// <summary>
        /// 向所有已打开的窗体发送Excel选择变更消息
        /// </summary>
        private static void NotifyFormsSelectionChange(Range target, string message = null)
        { _formManager.NotifyFormsSelectionChange(target, message); }

        /// <summary>
        /// 加载辅助窗体
        /// </summary>
        /// <remarks>此方法用于加载长字符串显示辅助窗体和下拉选择输入辅助窗体。 如果不启用辅助窗口或Excel是从OLE打开的,则不会加载这些窗体。</remarks>
        public static void LoadHelpForm()
        {
            if (!GlobalSettings.EnableHelpForm)
                return;  //WPS不启用辅助窗口，有不明冲突
            if (ETExcelExtensions.IsOpenedFromOLE())
                return;

            try
            {
                // 设置长字符串显示辅助窗体
                LongStringDisplayForm.ShowInTaskbar = false;
                OpenForm(LongStringDisplayForm, XlFormPosition.Outside);

                // 设置下拉选择输入辅助窗体
                DropdownInputForm.ShowInTaskbar = false;
                OpenForm(DropdownInputForm, XlFormPosition.Outside);

                HideToolsWindows();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("初始化辅助窗体失败", ex);
            }
        }

        /// <summary>
        /// 隐藏工具窗口
        /// </summary>
        /// <remarks>此方法用于隐藏高亮覆盖层、长字符串显示辅助窗体和下拉选择输入辅助窗体。 如果不启用辅助窗口，则此方法不执行任何操作。</remarks>
        public static void HideToolsWindows()
        {
            if (!GlobalSettings.EnableHelpForm)
                return;

            CrosshairOverlayForm?.HideWindow();
            LongStringDisplayForm?.HideWindow();
            DropdownInputForm?.HideWindow();
        }

        /// <summary>
        /// 加载高亮覆盖层窗体
        /// </summary>
        /// <remarks>此方法用于加载高亮覆盖层窗体。 如果是WPS或Excel是从OLE打开的,则不会加载此窗体。</remarks>
        public static void LoadHighlightOverlayForm()
        {
            if (GlobalSettings.IsWps)
                return; //WPS本身有高亮显示
            if (ETExcelExtensions.IsOpenedFromOLE())
                return;

            ETForm.NoActivateStyle(CrosshairOverlayForm);
            CrosshairOverlayForm.ShowInTaskbar = false;

            OpenForm(CrosshairOverlayForm, XlFormPosition.Outside);

            CrosshairOverlayForm.HideWindow();
        }

        /// <summary>
        /// 重新加载高亮覆盖层窗体
        /// </summary>
        /// <remarks>此方法用于重新加载高亮覆盖层窗体。 如果是WPS，则不执行任何操作。 方法会获取当前选择的范围和工作区域的矩形，然后调用重载方法更新高亮覆盖层的位置。</remarks>
        public static void ReLoadHighlightOverlayForm()
        {
            if (GlobalSettings.IsWps)
                return;//WPS本身有高亮显示

            ExcelApplication = Globals.ThisAddIn.Application;
            Range selectRange = ExcelApplication.Selection;

            if (ExcelApplication == null || selectRange == null)
                return;

            WinAPI.RECT rangeRect = ETForm.RangeRect(ExcelApplication, selectRange);
            WinAPI.RECT areaRect = ETForm.WorksheetWorkingAreaRect(ExcelApplication);

            // 重新加载十字光标
            ReLoadHighlightOverlayForm(areaRect, rangeRect);
        }

        /// <summary>
        /// 重新加载高亮覆盖层窗体
        /// </summary>
        /// <param name="areaRect">工作区域的矩形</param>
        /// <param name="rangeRect">选择范围的矩形</param>
        /// <remarks>此方法用于根据给定的工作区域和选择范围的矩形重新加载高亮覆盖层窗体。 如果是WPS，则不执行任何操作。 根据ribbon上的复选框状态决定是否新高亮覆盖层的位或隐藏它。</remarks>
        public static void ReLoadHighlightOverlayForm(WinAPI.RECT areaRect, WinAPI.RECT rangeRect)
        {
            if (GlobalSettings.IsWps)
                return;//WPS本身有高亮显示

            if (Globals.Ribbons.hyRibbon.checkBoxHorizontalHighlight.Checked || Globals.Ribbons.hyRibbon.checkBoxVerticalHighlight.Checked)
                CrosshairOverlayForm.UpdatePosition(Globals.ThisAddIn.Application, areaRect, rangeRect);
            else
                CrosshairOverlayForm.HideWindow();
        }

        #endregion

        #region 重置菜单

        /// <summary>
        /// 重构所有菜单
        /// </summary>
        /// <remarks>此方法用于重新构建Excel应用程序中的所有菜单。 它会清除现有的按钮列表和命令栏按钮集合，然后重置所有命令栏到默认状态。 之后，它会更新工作表右键菜单和工作表标签右键菜单。</remarks>
        //public static void RebuildAllMenus()
        //{
        //    try
        //    {
        //        if (MenuManagerInstance != null)
        //        {
        //            lock (_menuLock)
        //            {
        //                MenuManagerInstance.RebuildAllMenus();
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        ETLogManager.Error("重建菜单时出错", ex);
        //    }
        //}
        #endregion

        #region CustomTaskPane管理

        /// <summary>
        /// 加载任务窗格
        /// </summary>
        /// <param name="control">要加载的用户控件</param>
        /// <param name="title">任务窗格标题</param>
        /// <param name="width">任务窗格宽度（默认250）</param>
        /// <param name="dockPosition">停靠位置（默认右侧）</param>
        /// <exception cref="ETException">加载任务窗格失败时抛出</exception>
        public static void LoadTaskPane(UserControl control, string title, int width = DEFAULT_TASKPANE_WIDTH,
            MsoCTPDockPosition dockPosition = MsoCTPDockPosition.msoCTPDockPositionRight)
        {
            try
            {
                _taskPaneManager.LoadTaskPane(control, title, width, dockPosition);
            }
            catch (Exception ex)
            {
                string errorMessage = $"加载任务窗格失败: {title}";
                ETLogManager.Error(errorMessage, ex);
                throw new ETException(errorMessage, "TaskPaneOperation", ex);
            }
        }

        /// <summary>
        /// 卸载任务窗格
        /// </summary>
        /// <param name="title">要卸载的任务窗格标题</param>
        /// <exception cref="ETException">卸载任务窗格失败时抛出</exception>
        public static void UnloadTaskPane(string title)
        {
            try
            {
                _taskPaneManager.UnloadTaskPane(title);
            }
            catch (Exception ex)
            {
                string errorMessage = $"卸载任务窗格失败: {title}";
                ETLogManager.Error(errorMessage, ex);
                throw new ETException(errorMessage, "TaskPaneOperation", ex);
            }
        }

        /// <summary>
        /// 获取任务窗格中的用户控件
        /// </summary>
        /// <typeparam name="T">用户控件类型</typeparam>
        /// <param name="title">任务窗格标题</param>
        /// <returns>指定类型的用户控件实例</returns>
        /// <exception cref="ETException">获取任务窗格控件失败时抛出</exception>
        public static T GetTaskPaneControl<T>(string title) where T : UserControl
        {
            try
            {
                T control = _taskPaneManager.GetTaskPaneControl<T>(title);
                return control;
            }
            catch (Exception ex)
            {
                string errorMessage = $"获取任务窗格控件失败: {title}, 类型: {typeof(T).Name}";
                ETLogManager.Error(errorMessage, ex);
                throw new ETException(errorMessage, "TaskPaneOperation", ex);
            }
        }

        #endregion

        /// <summary>
        /// 任务窗格管理器实例
        /// </summary>
        /// <remarks>用于管理Excel任务窗格的创建、销毁和状态维护</remarks>
        private static readonly TaskPaneManager _taskPaneManager = new();

        /// <summary>
        /// 获取通信服务实例
        /// </summary>
        public ETCommunicationService CommunicationService { get; private set; }

        /// <summary>
        /// 初始化通信服务
        /// </summary>
        private void InitializeCommunicationService()
        {
            try
            {
                // 从stock.ini读取服务器端口
                ETIniFile stockConfig = new(ETConfig.GetConfigDirectory("stock.ini"));
                string portValue = stockConfig.GetValue("CommunicationService", "sendPort");

                // 安全解析端口号，提供默认值
                if (!int.TryParse(portValue, out int serverPort))
                {
                    ETLogManager.Warning($"无法解析端口号 '{portValue}'，使用默认端口 {DEFAULT_COMMUNICATION_PORT}");
                    serverPort = DEFAULT_COMMUNICATION_PORT;
                }

                // 通信服务使用指定端口

                // 作为客户端，使用指定端口
                CommunicationService = new ETCommunicationService(false, serverPort, false);
                CommunicationService.OnConnectionStateChanged += HandleConnectionStateChanged;
                // 不再自动连接，改为按需连接

                ETLogManager.Info("通信服务初始化成功");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("初始化通信服务时出错", ex);
            }
        }

        /// <summary>
        /// 在需要时连接到服务器
        /// </summary>
        /// <returns>连接是否成功</returns>
        public async Task<bool> ConnectToServerIfNeeded()
        {
            try
            {
                if (CommunicationService == null)
                {
                    ETLogManager.Warning("通信服务未初始化");
                    return false;
                }

                if (CommunicationService.IsConnected)
                {
                    return true;
                }

                // 从stock.ini读取服务器IP
                ETIniFile stockConfig = new(ETConfig.GetConfigDirectory("stock.ini"));
                string serverIP = stockConfig.GetValue("CommunicationService", "sendIP");

                // 连接到指定的服务器IP
                bool connected = await CommunicationService.ConnectAsync(serverIP).ConfigureAwait(false);
                if (!connected)
                {
                    ETLogManager.Warning("无法连接到HyAssistant，部分功能可能无法使用。");
                }
                return connected;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("连接HyAssistant时出错", ex);
                return false;
            }
        }

        /// <summary>
        /// 处理连接状态变化
        /// </summary>
        private void HandleConnectionStateChanged(bool isConnected)
        {
            try
            {
                if (!isConnected)
                {
                    ETLogManager.Info("与HyAssistant的连接已断开");
                }
                else
                {
                    ETLogManager.Info("已连接到HyAssistant");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("处理连接状态变化时出错", ex);
            }
        }
    }

    public enum XlFormPosition
    {
        Center,
        Outside,
        Right,
        ShowDialog,
        NoControl
    }
}