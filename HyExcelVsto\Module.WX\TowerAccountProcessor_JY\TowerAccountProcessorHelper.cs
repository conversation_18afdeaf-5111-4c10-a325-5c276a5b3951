using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using Microsoft.Office.Interop.Excel;
using ET;
using HyExcelVsto.Extensions;
using ExtensionsTools;

namespace HyExcelVsto.Module.WX.TowerAccountProcessor
{
    /// <summary>
    /// 铁塔内部台账处理器帮助类 提供铁塔内部台账梳理和数据汇总功能
    /// </summary>
    public static class TowerAccountProcessorHelper
    {
        #region 常量定义

        /// <summary>
        /// 审核人员列表
        /// </summary>
        public static readonly string[] AUDITOR_NAMES = new string[]
        {
            "旭斌", "海颖", "丽明", "泽淦", "陈丹", "世俊", "家胜", "恒彬",
            "弋涵", "练津", "梓嘉", "宇哲", "冠武", "小楠", "育林", "德宏"
        };

        /// <summary>
        /// 需要汇总的列标题
        /// </summary>
        public static readonly string[] SUMMARY_COLUMNS = new string[]
        {
            "需求编号", "区域", "批次", "需求站点", "需求经度", "需求纬度", "建设类型",
            "铁塔站名", "原产权方", "实际经度", "实际纬度", "站点属性", "确认方案",
            "评审意见", "备注", "审核人员", "会审日期"
        };

        /// <summary>
        /// 序号列标题
        /// </summary>
        public const string SEQUENCE_NUMBER_COLUMN = "序号";

        #endregion 常量定义

        #region 结果类定义

        /// <summary>
        /// 处理结果
        /// </summary>
        public class ProcessingResult
        {
            /// <summary>
            /// 是否成功
            /// </summary>
            public bool Success { get; set; }

            /// <summary>
            /// 处理的行数
            /// </summary>
            public int ProcessedRows { get; set; }

            /// <summary>
            /// 新工作表名称
            /// </summary>
            public string NewWorksheetName { get; set; }

            /// <summary>
            /// 错误消息
            /// </summary>
            public string ErrorMessage { get; set; }

            /// <summary>
            /// 详细消息
            /// </summary>
            public string Message { get; set; }
        }

        /// <summary>
        /// 汇总结果
        /// </summary>
        public class SummaryResult
        {
            /// <summary>
            /// 是否成功
            /// </summary>
            public bool Success { get; set; }

            /// <summary>
            /// 插入的行数
            /// </summary>
            public int InsertedRows { get; set; }

            /// <summary>
            /// 插入的数据范围
            /// </summary>
            public Range InsertedRange { get; set; }

            /// <summary>
            /// 错误消息
            /// </summary>
            public string ErrorMessage { get; set; }

            /// <summary>
            /// 详细消息
            /// </summary>
            public string Message { get; set; }
        }

        #endregion 结果类定义

        #region 铁塔内部台账提取电信部分

        /// <summary>
        /// 提取铁塔内部台账的电信部分
        /// </summary>
        /// <param name="sourceWorksheet">源工作表</param>
        /// <returns>处理结果</returns>
        public static ProcessingResult ExtractTelecomPart(Worksheet sourceWorksheet = null)
        {
            try
            {
                ETLogManager.Info("开始提取铁塔内部台账电信部分");

                // 获取源工作表
                if (sourceWorksheet == null)
                {
                    sourceWorksheet = Globals.ThisAddIn?.Application?.ActiveSheet;
                }

                if (sourceWorksheet == null)
                {
                    return new ProcessingResult
                    {
                        Success = false,
                        ErrorMessage = "未找到活动工作表"
                    };
                }

                // 验证工作表类型
                var cellValue = sourceWorksheet.Cells[1, 1].Value?.ToString()?.Trim();
                if (string.IsNullOrEmpty(cellValue) || !cellValue.EndsWith("三家运营商会审汇总表"))
                {
                    return new ProcessingResult
                    {
                        Success = false,
                        ErrorMessage = "只能操作铁塔设计内部台账（标题应以'三家运营商会审汇总表'结尾）"
                    };
                }

                // 复制工作表到新工作簿
                Worksheet newWorksheet = sourceWorksheet.CopyWorksheetToNewWorkbook();

                // 取消所有筛选
                if (newWorksheet.AutoFilterMode)
                {
                    newWorksheet.AutoFilterMode = false;
                }

                // 删除首行
                ((Range)newWorksheet.Rows[1]).Delete(XlDeleteShiftDirection.xlShiftUp);
                Range filterRow = newWorksheet.Rows[1];
                filterRow.AutoFilter(1, Type.Missing, XlAutoFilterOperator.xlAnd, Type.Missing, true);

                // 动态找到"审核人员"列并筛选
                int reviewColumn = ETExcelExtensions.FindColumnByHeaderTitle("审核人员", filterRow)?.Column ?? 0;
                if (reviewColumn > 0)
                {
                    // 启动筛选
                    Range firstCell = newWorksheet.Cells[2, reviewColumn]; // 从第2行开始,跳过标题行
                    Range lastCell = newWorksheet.Cells[newWorksheet.UsedRange.Rows.Count, reviewColumn];
                    Range rangeToFilter = newWorksheet.Range[firstCell, lastCell];

                    // 筛选掉不需要的审核人员
                    ETExcelExtensions.BatchDeleteRowsByCriteria(rangeToFilter, AUDITOR_NAMES, false);
                }

                // 取消筛选
                if (newWorksheet.AutoFilterMode)
                {
                    newWorksheet.AutoFilterMode = false;
                }

                // 动态找到"会审日期"和"序号"列并排序
                int auditDateColumn = FindColumnByName(newWorksheet, "会审日期");
                int orderColumn = FindColumnByName(newWorksheet, "序号");

                if (auditDateColumn > 0 && orderColumn > 0)
                {
                    newWorksheet.UsedRange.Sort(
                        newWorksheet.UsedRange.Columns[auditDateColumn],
                        XlSortOrder.xlDescending,  // 会审日期降序
                        newWorksheet.UsedRange.Columns[orderColumn],
                        Type.Missing,
                        XlSortOrder.xlAscending, // 序号升序
                        Type.Missing,
                        XlSortOrder.xlAscending,
                        XlYesNoGuess.xlGuess,
                        Type.Missing,
                        Type.Missing,
                        XlSortOrientation.xlSortColumns,
                        XlSortMethod.xlPinYin,
                        XlSortDataOption.xlSortNormal,
                        XlSortDataOption.xlSortNormal,
                        XlSortDataOption.xlSortNormal);
                }

                // 设定第一行为筛选行
                Range firstRow = newWorksheet.Rows[1];
                firstRow.AutoFilter(1, Type.Missing, XlAutoFilterOperator.xlAnd, Type.Missing, true);

                // 设置冻结窗格
                newWorksheet.Range["F2"].Select();
                Globals.ThisAddIn.Application.ActiveWindow.FreezePanes = true;

                // 将工作表名称复制到剪贴板
                Clipboard.SetText(newWorksheet.Name);

                ETLogManager.Info($"铁塔内部台账电信部分提取完成，新工作表：{newWorksheet.Name}");

                return new ProcessingResult
                {
                    Success = true,
                    NewWorksheetName = newWorksheet.Name,
                    Message = $"提取完成！新工作表：{newWorksheet.Name}（已复制到剪贴板）"
                };
            }
            catch (Exception ex)
            {
                ETLogManager.Error("提取铁塔内部台账电信部分失败", ex);
                return new ProcessingResult
                {
                    Success = false,
                    ErrorMessage = $"提取过程中发生错误：{ex.Message}"
                };
            }
        }

        #endregion 铁塔内部台账提取电信部分

        #region 汇总新铁塔台账到铁塔会审台账

        /// <summary>
        /// 汇总新铁塔台账到铁塔会审台账
        /// </summary>
        /// <returns>汇总结果</returns>
        public static SummaryResult SummarizeToAuditAccount()
        {
            try
            {
                ETLogManager.Info("开始汇总新铁塔台账到铁塔会审台账");

                ETExcelExtensions.SetAppFastMode();

                var result = SummarizeData();
                if (result.Success && result.InsertedRange != null)
                {
                    // 复制公式到新插入的行
                    CopyFormulasAndConvertToValues(result.InsertedRange);

                    result.Message = $"数据汇总完成！新插入了 {result.InsertedRows} 行数据。";
                    ETLogManager.Info(result.Message);
                }

                return result;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("汇总新铁塔台账失败", ex);
                return new SummaryResult
                {
                    Success = false,
                    ErrorMessage = $"汇总过程中发生错误：{ex.Message}"
                };
            }
            finally
            {
                ETExcelExtensions.SetAppNormalMode(true);
            }
        }

        #endregion 汇总新铁塔台账到铁塔会审台账

        #region 辅助方法

        /// <summary>
        /// 根据列名查找列号
        /// </summary>
        /// <param name="worksheet">工作表</param>
        /// <param name="columnName">列名</param>
        /// <param name="titleRow">标题行号</param>
        /// <returns>列号，未找到返回0</returns>
        public static int FindColumnByName(Worksheet worksheet, string columnName, int titleRow = 1)
        {
            try
            {
                Range firstRow = worksheet.Rows[titleRow];
                firstRow = firstRow.OptimizeRangeSize();
                for (int i = 1; i <= worksheet.UsedRange.Columns.Count; i++)
                {
                    string value = firstRow?.Cells[1, i]?.Value?.ToString();
                    if (!string.IsNullOrEmpty(value) && value == columnName)
                    {
                        return i;
                    }
                }
                return 0; // 未找到列
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"查找列 '{columnName}' 失败", ex);
                return 0;
            }
        }

        /// <summary>
        /// 获取功能说明信息
        /// </summary>
        /// <returns>功能说明</returns>
        public static string GetFunctionDescription()
        {
            return "铁塔内部台账梳理功能：\r\n\r\n" +
                   "1. 铁塔内部台账提取电信部分：\r\n" +
                   "   - 验证工作表类型（必须是'三家运营商会审汇总表'）\r\n" +
                   "   - 复制工作表到新工作簿\r\n" +
                   "   - 删除首行并设置筛选\r\n" +
                   "   - 根据审核人员筛选电信相关数据\r\n" +
                   "   - 按会审日期降序、序号升序排序\r\n" +
                   "   - 设置冻结窗格和筛选行\r\n\r\n" +
                   "2. 汇总新铁塔台账到铁塔会审台账：\r\n" +
                   "   - 选择来源表和汇总表\r\n" +
                   "   - 根据会审日期去重汇总数据\r\n" +
                   "   - 复制公式并转换为数值\r\n" +
                   "   - 自动插入新数据到汇总表\r\n\r\n" +
                   "注意：本功能针对性强，代码是硬编码，如需改动需直接修改代码";
        }

        /// <summary>
        /// 将来源表数据汇总到汇总表中
        /// </summary>
        /// <returns>汇总结果</returns>
        private static SummaryResult SummarizeData()
        {
            try
            {
                // 获取来源表和汇总表的工作表对象
                Worksheet sourceSheet = ETExcelExtensions.GetRangeSelection("请选择来源表所在单元格").Worksheet;
                Worksheet summarySheet = ETExcelExtensions.GetRangeSelection("请选择汇总表所在单元格").Worksheet;

                if (sourceSheet == null || summarySheet == null)
                {
                    return new SummaryResult
                    {
                        Success = false,
                        ErrorMessage = "未能获取来源表或汇总表"
                    };
                }

                // 获取筛选行号
                int sourceFilterRow = ETExcelExtensions.GetWorksheetFilterRowNumber(sourceSheet);
                int summaryFilterRow = ETExcelExtensions.GetWorksheetFilterRowNumber(summarySheet);

                // 获取表头范围
                Range sourceHeaderRange = sourceSheet.Rows[sourceFilterRow];
                Range summaryHeaderRange = summarySheet.Range[
                    summarySheet.Rows[1],
                    summarySheet.Rows[summaryFilterRow]];

                // 查找来源表和汇总表中对应的列
                Dictionary<string, int> sourceColumns = new Dictionary<string, int>();
                Dictionary<string, int> summaryColumns = new Dictionary<string, int>();

                foreach (string columnTitle in SUMMARY_COLUMNS)
                {
                    Range sourceCell = ETExcelExtensions.FindColumnByHeaderTitle(columnTitle, sourceHeaderRange);
                    Range summaryCell = ETExcelExtensions.FindColumnByHeaderTitle(columnTitle, summaryHeaderRange);

                    if (sourceCell != null && summaryCell != null)
                    {
                        sourceColumns[columnTitle] = sourceCell.Column;
                        summaryColumns[columnTitle] = summaryCell.Column;
                    }
                    else
                    {
                        return new SummaryResult
                        {
                            Success = false,
                            ErrorMessage = $"未找到列 '{columnTitle}'，无法进行汇总操作。"
                        };
                    }
                }

                // 获取已汇总的会审日期
                HashSet<string> existingDates = new HashSet<string>();
                Range summaryUsedRange = summarySheet.UsedRange;
                int summaryAuditDateColumn = summaryColumns["会审日期"];
                for (int row = summaryFilterRow + 1; row <= summaryUsedRange.Rows.Count; row++)
                {
                    dynamic date = summarySheet.Cells[row, summaryAuditDateColumn].Text;
                    if (!string.IsNullOrEmpty(date))
                    {
                        existingDates.Add(date);
                    }
                }

                // 收集需要插入的新数据
                List<object[]> newData = new List<object[]>();
                Range sourceUsedRange = sourceSheet.UsedRange;
                int sourceAuditDateColumn = sourceColumns["会审日期"];
                for (int sourceRow = sourceFilterRow + 1; sourceRow <= sourceUsedRange.Rows.Count; sourceRow++)
                {
                    string auditDate = sourceSheet.Cells[sourceRow, sourceAuditDateColumn].Text;
                    if (!existingDates.Contains(auditDate))
                    {
                        object[] rowData = new object[SUMMARY_COLUMNS.Length];
                        for (int i = 0; i < SUMMARY_COLUMNS.Length; i++)
                        {
                            rowData[i] = sourceSheet.Cells[sourceRow, sourceColumns[SUMMARY_COLUMNS[i]]].Value;
                        }
                        newData.Add(rowData);
                    }
                }

                Range insertedRange = null;

                // 一次性插入新行
                if (newData.Count > 0)
                {
                    int insertRow = summaryFilterRow + 1; // 从标题行的下一行开始插入
                    Range insertRange = summarySheet.Rows[insertRow].Resize[newData.Count];
                    insertRange.Insert(XlInsertShiftDirection.xlShiftDown);

                    // 填充数据
                    for (int i = 0; i < newData.Count; i++)
                    {
                        for (int j = 0; j < SUMMARY_COLUMNS.Length; j++)
                        {
                            summarySheet.Cells[insertRow + i, summaryColumns[SUMMARY_COLUMNS[j]]].Value = newData[i][j];
                        }
                    }

                    // 设置插入的行范围
                    insertedRange = summarySheet.Range[
                        summarySheet.Cells[insertRow, 1],
                        summarySheet.Cells[insertRow + newData.Count - 1, summarySheet.UsedRange.Columns.Count]];
                }

                return new SummaryResult
                {
                    Success = true,
                    InsertedRows = newData.Count,
                    InsertedRange = insertedRange,
                    Message = newData.Count > 0 ? $"成功插入 {newData.Count} 行新数据" : "没有新数据需要汇总"
                };
            }
            catch (Exception ex)
            {
                ETLogManager.Error("汇总数据失败", ex);
                return new SummaryResult
                {
                    Success = false,
                    ErrorMessage = $"汇总数据时发生错误：{ex.Message}"
                };
            }
        }

        /// <summary>
        /// 复制公式到新插入的行，执行计算，然后转换为纯数据
        /// </summary>
        /// <param name="insertedRange">新插入的数据范围</param>
        private static void CopyFormulasAndConvertToValues(Range insertedRange)
        {
            try
            {
                if (insertedRange == null) return;

                Worksheet sheet = insertedRange.Worksheet;

                // 在前3行内查找"序号"所在的列
                Range headerRows = sheet.Range[sheet.Rows[1], sheet.Rows[3]];
                Range serialNumberCell = headerRows.Find(SEQUENCE_NUMBER_COLUMN, LookAt: XlLookAt.xlWhole);
                if (serialNumberCell == null)
                {
                    ETLogManager.Warning($"未在前3行找到'{SEQUENCE_NUMBER_COLUMN}'列，无法复制公式。");
                    return;
                }

                int serialNumberColumn = serialNumberCell.Column;

                // 复制公式范围（从第1行获取）
                Range sourceFormulas = sheet.Range[sheet.Cells[1, 1], sheet.Cells[1, serialNumberColumn]];

                // 定义目标范围
                Range targetRange = sheet.Range[
                    sheet.Cells[insertedRange.Row, 1],
                    sheet.Cells[insertedRange.Row + insertedRange.Rows.Count - 1, serialNumberColumn]];

                // 复制公式
                sourceFormulas.Copy(targetRange);

                // 强制执行表格计算
                sheet.Calculate();

                // 将公式结果转换为纯数据
                targetRange.Value = targetRange.Value;

                ETLogManager.Info("公式复制和转换完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("复制公式和转换数值失败", ex);
            }
        }

        #endregion 辅助方法
    }
}