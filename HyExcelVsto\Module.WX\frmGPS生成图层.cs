﻿using ET;

using Microsoft.Office.Interop.Excel;
using NetTopologySuite.Geometries;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Windows.Forms;

using ExtensionsTools;

namespace HyExcelVsto.Module.WX
{
    /// <summary>
    /// GPS坐标生成KML图层窗体
    /// 本窗体用于将Excel表格中的GPS坐标数据（经纬度）转换为KML文件，可生成点位或多边形图层
    /// KML (Keyhole Markup Language) 是一种基于XML的标记语言，用于在地理信息系统软件中表示地理数据
    /// </summary>
    public partial class frmGPS生成图层 : Form
    {
        #region 常量
        /// <summary>
        /// 最大允许的数据行数，限制处理数据量，避免性能问题
        /// </summary>
        const int MaxRowCount = 100000;

        /// <summary>
        /// 窗体高度 - 默认模式
        /// 定义窗体的标准高度，用于初始化和重置窗体大小
        /// </summary>
        const int DefaultFormHeight = 189;

        /// <summary>
        /// 窗体宽度 - 默认模式
        /// 定义窗体的标准宽度，用于初始化和重置窗体大小
        /// </summary>
        const int DefaultFormWidth = 571;
        #endregion

        /// <summary>
        /// 初始化GPS生成图层窗体
        /// 构造函数中进行窗体初始化、大小设置和文件过滤器配置
        /// </summary>
        public frmGPS生成图层()
        {
            InitializeComponent();

            // 设置窗体大小（当前已注释，但保留以备后续使用）
            //this.Size = new System.Drawing.Size(DefaultFormWidth, DefaultFormHeight);
            //this.MinimumSize = new System.Drawing.Size(DefaultFormWidth, DefaultFormHeight);
            //this.MaximumSize = new System.Drawing.Size(DefaultFormWidth, DefaultFormHeight);

            // 设置文件选择控件的过滤器，限制用户只能选择KML文件或所有文件
            file_Point.FileFilter = "KML文件|*.kml|所有文件|*.*";
            file_Point.DefaultFileExtension = ".kml";
        }

        #region 事件处理
        /// <summary>
        /// 生成KML文件按钮点击事件处理
        /// 获取用户选择的数据范围、站点名称、备注和输出路径，然后生成KML文件
        /// 成功后将文件路径复制到剪贴板，方便用户使用
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        void button生成KML_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取并验证输入范围，包括GPS数据、站点名称和备注范围
                (Range GpsDataRange, Range StationNameRange, Range StationRemarkRange, string OutputPath) ranges = GetAndValidateRanges();

                // 调用方法生成KML文件，根据用户选择的模式（点或多边形）
                GenerateKmlFile(ranges.GpsDataRange, ranges.StationNameRange, ranges.StationRemarkRange, ranges.OutputPath);

                // 将文件路径复制到剪贴板，方便用户快速访问
                Clipboard.SetText(ranges.OutputPath);
            }
            catch (ETException)
            {
                // 直接抛出ETException，保留原始异常信息
                throw;
            }
            catch (Exception ex)
            {
                // 记录其他异常并包装为ETException
                ETLogManager.Error(ex);
                throw new ETException("生成KML文件过程出现错误", "KML文件生成操作", ex);
            }
        }

        /// <summary>
        /// 窗体加载事件处理
        /// 初始化GPS列选择，并设置默认的KML文件保存路径
        /// 默认路径基于当前工作簿和工作表名称，以及当前时间
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        void frmGPS生成图层_Load(object sender, EventArgs e)
        {
            try
            {
                // 初始化GPS列选择，自动查找经纬度列
                InitializeGpsColumns();

                // 从配置文件读取默认保存目录
                string directory = ThisAddIn.ConfigurationSettings.GetValue("file", "kmlfolder");
                if (!string.IsNullOrEmpty(directory))
                {
                    // 如果目录不存在则创建
                    if (!Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    // 获取当前工作簿和工作表名称，用于生成文件名
                    string workbookName = Path.GetFileNameWithoutExtension(Globals.ThisAddIn.Application.ActiveWorkbook.Name);
                    string worksheetName = Globals.ThisAddIn.Application.ActiveSheet.Name;

                    // 生成完整的文件路径，使用格式：{文件名}-{工作表名}-{yyyyMMddHHmmss}
                    string defaultPath = Path.Combine(directory, $"{workbookName}-{worksheetName}-{DateTime.Now:yyyyMMddHHmmss}.kml");
                    file_Point.Text = defaultPath;
                }
            }
            catch (Exception ex)
            {
                // 记录异常并抛出包装后的异常
                ETLogManager.Error(ex);
                throw new ETException("窗体加载过程出现错误", "窗体初始化操作", ex);
            }
        }

        /// <summary>
        /// 复制文件路径到剪贴板
        /// 将当前选择的KML文件路径复制到剪贴板，方便用户粘贴使用
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        void 复制路径到剪贴板ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取文件路径并验证
                string filePath = file_Point.Text;
                ValidateFilePath(filePath, "复制");

                // 复制到剪贴板
                Clipboard.SetText(filePath);
            }
            catch (Exception ex)
            {
                // 记录异常并抛出包装后的异常
                ETLogManager.Error(ex);
                throw new ETException("复制文件路径过程出现错误", "剪贴板操作", ex);
            }
        }

        /// <summary>
        /// 在资源管理器中打开文件所在目录
        /// 使用Windows资源管理器打开并选中当前KML文件
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        void 打开所在目录ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                // 如果文件路径不为空，启动资源管理器并选中该文件
                if (!string.IsNullOrEmpty(file_Point.Text))
                {
                    // /select参数使资源管理器选中指定文件
                    Process.Start("explorer.exe", $"/select,\"{file_Point.Text}\"");
                }
            }
            catch (Exception ex)
            {
                // 记录异常并显示错误消息
                ETLogManager.Error(ex);
                MessageBox.Show($"打开目录失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 生成新文件名事件处理
        /// 根据当前工作簿和工作表名称以及当前时间生成新的KML文件名
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        void 生成新名字ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(file_Point.Text))
                {
                    // 获取当前文件的目录和扩展名
                    string directory = Path.GetDirectoryName(file_Point.Text);
                    string extension = Path.GetExtension(file_Point.Text);

                    // 获取当前工作簿和工作表名称
                    string workbookName = Path.GetFileNameWithoutExtension(Globals.ThisAddIn.Application.ActiveWorkbook.Name);
                    string worksheetName = Globals.ThisAddIn.Application.ActiveSheet.Name;

                    // 生成新文件名，使用格式：{文件名}-{工作表名}-{yyyyMMddHHmmss}
                    string newFileName = $"{workbookName}-{worksheetName}-{DateTime.Now:yyyyMMddHHmmss}{extension}";
                    string newPath = Path.Combine(directory, newFileName);

                    // 更新文件路径文本框
                    file_Point.Text = newPath;
                }
            }
            catch (Exception ex)
            {
                // 记录异常并显示错误消息
                ETLogManager.Error(ex);
                MessageBox.Show($"生成新文件名失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        #endregion

        #region 辅助方法
        /// <summary>
        /// 初始化GPS列选择
        /// 自动查找当前工作表中可能包含经纬度数据的列，并设置为默认选择
        /// 同时设置站点名称列为经纬度列的前一列
        /// </summary>
        void InitializeGpsColumns()
        {
            // 寻找工作表第一行中可能包含经纬度数据的列
            Range latLongColumns = ETExcelExtensions.FindLatLongColumns(Globals.ThisAddIn.Application.ActiveSheet, 1);
            if (latLongColumns?.Column > 1)
            {
                // 设置经纬度列为默认选择
                ucERS_Point_来源经纬度.SelectedRange = latLongColumns.EntireColumn;

                // 设置站点名称列为经纬度列的前一列
                ucERS_Point_来源名称.SelectedRange = ucERS_Point_来源经纬度.SelectedRange.Offset[0, -1].Columns[1];
            }
        }

        /// <summary>
        /// 获取并验证所需的所有范围
        /// 验证用户选择的GPS数据范围、站点名称范围、备注范围以及输出路径
        /// </summary>
        /// <returns>包含验证后的GPS数据范围、站点名称范围、备注范围和输出路径的元组</returns>
        (Range GpsDataRange, Range StationNameRange, Range StationRemarkRange, string OutputPath) GetAndValidateRanges()
        {
            // 获取GPS数据范围（必需）
            Range gpsDataRange = ucERS_Point_来源经纬度.SelectedRange;
            if (gpsDataRange == null)
            {
                throw new ETException("请选择GPS数据范围", "KML文件生成操作");
            }

            // 获取站点名称和备注范围（可选，名称默认为GPS数据范围）
            Range stationNameRange = ucERS_Point_来源名称.SelectedRange ?? gpsDataRange;
            Range stationRemarkRange = ucERS_Point_来源注释.SelectedRange;

            // 筛选有效GPS数据并获取可见范围，排除隐藏行
            gpsDataRange = gpsDataRange.OptimizeRangeSize().GetVisibleRange();

            // 处理点名和备注列，仅取第一列数据（若有）
            if (stationNameRange != null)
            {
                stationNameRange = stationNameRange.Cells[1, 1].EntireColumn;
            }
            if (stationRemarkRange != null)
            {
                stationRemarkRange = stationRemarkRange.Cells[1, 1].EntireColumn;
            }

            // 验证输出路径
            string outputPath = file_Point.Text;
            if (string.IsNullOrWhiteSpace(outputPath))
            {
                throw new ETException("请指定KML文件保存路径", "KML文件生成操作");
            }

            // 返回验证后的所有范围和路径
            return (gpsDataRange, stationNameRange, stationRemarkRange, outputPath);
        }

        /// <summary>
        /// 生成KML文件
        /// 根据选择的模式（点位或多边形）调用相应的KML生成方法
        /// </summary>
        /// <param name="gpsDataRange">GPS数据范围</param>
        /// <param name="stationNameRange">站点名称范围</param>
        /// <param name="stationRemarkRange">站点备注范围</param>
        /// <param name="outputPath">输出文件路径</param>
        void GenerateKmlFile(Range gpsDataRange, Range stationNameRange, Range stationRemarkRange, string outputPath)
        {
            // 根据单选按钮选择调用对应的KML生成方法
            if (radioButtonPolygon.Checked)
            {
                // 生成多边形KML文件
                GeneratePolygonKml(gpsDataRange, stationNameRange, stationRemarkRange, outputPath);
            }
            else
            {
                // 生成点位KML文件
                GeneratePointKml(gpsDataRange, stationNameRange, stationRemarkRange, outputPath);
            }
        }

        /// <summary>
        /// 验证文件路径
        /// 检查文件路径是否为空或只包含空白字符
        /// </summary>
        /// <param name="filePath">要验证的文件路径</param>
        /// <param name="operation">当前执行的操作名称，用于错误提示</param>
        void ValidateFilePath(string filePath, string operation)
        {
            if (string.IsNullOrWhiteSpace(filePath))
            {
                throw new ETException($"文件路径为空，无法{operation}", "文件路径验证");
            }
        }
        #endregion

        #region KML生成方法
        /// <summary>
        /// 生成点位类型的KML文件
        /// 处理GPS数据，将每个经纬度点转换为KML点位，并生成KML文件
        /// </summary>
        /// <param name="gpsDataRange">包含GPS经纬度数据的Excel范围</param>
        /// <param name="stationNameRange">包含站点名称的Excel范围</param>
        /// <param name="stationRemarkRange">包含站点备注的Excel范围</param>
        /// <param name="kmlOutputPath">KML文件输出路径</param>
        void GeneratePointKml(Range gpsDataRange, Range stationNameRange, Range stationRemarkRange, string kmlOutputPath)
        {
            try
            {
                // 验证输入参数
                ValidateKmlInputParameters(kmlOutputPath, gpsDataRange, false);

                // 从Excel范围中提取GPS点位列表
                List<XlGpsPointAndRange> gpsPoints = ETGPS.ExtractGpsList(gpsDataRange);

                // 验证数据行数是否超出限制
                ValidateDataRowCount(gpsPoints.Count);

                // 创建KML点位列表
                List<XlGpsKmlPoint> kmlPoints = [];
                foreach (XlGpsPointAndRange currentGps in gpsPoints)
                {
                    // 为每个GPS点创建对应的KML点位对象
                    XlGpsKmlPoint point = CreateKmlPoint(currentGps, stationNameRange, stationRemarkRange, gpsDataRange);
                    kmlPoints.Add(point);
                }

                // 生成KML文件
                ETGPS.GeneratePointKmlFile(kmlPoints, kmlOutputPath);
            }
            catch (ETException)
            {
                // 直接抛出ETException异常
                throw;
            }
            catch (Exception ex)
            {
                // 记录其他异常并包装为ETException
                ETLogManager.Error(ex);
                throw new ETException("生成点位KML文件过程出现错误", "KML点位数据处理", ex);
            }
        }

        /// <summary>
        /// 生成多边形类型的KML文件
        /// 处理GPS数据，将每行数据转换为KML多边形，并生成KML文件
        /// </summary>
        /// <param name="gpsDataRange">包含多边形GPS数据的Excel范围</param>
        /// <param name="stationNameRange">包含站点名称的Excel范围</param>
        /// <param name="stationRemarkRange">包含站点备注的Excel范围</param>
        /// <param name="kmlOutputPath">KML文件输出路径</param>
        void GeneratePolygonKml(Range gpsDataRange, Range stationNameRange, Range stationRemarkRange, string kmlOutputPath)
        {
            try
            {
                // 验证输入参数，多边形模式下站点名称是必需的
                ValidateKmlInputParameters(kmlOutputPath, gpsDataRange, true);

                // 验证数据行数是否超出限制
                ValidateDataRowCount(gpsDataRange.Rows.Count);

                // 创建KML多边形列表
                List<XlGpsKmlPolygon> kmlPolygons = [];
                foreach (Range currentGps in gpsDataRange.Rows)
                {
                    // 为每行数据创建对应的KML多边形对象
                    XlGpsKmlPolygon polygon = CreateKmlPolygon(currentGps, stationNameRange, stationRemarkRange);
                    if (polygon != null)
                    {
                        kmlPolygons.Add(polygon);
                    }
                }

                // 生成KML文件
                ETGPS.GenerateKmlPolygonFile(kmlPolygons, kmlOutputPath);
            }
            catch (ETException)
            {
                // 直接抛出ETException异常
                throw;
            }
            catch (Exception ex)
            {
                // 记录其他异常并包装为ETException
                ETLogManager.Error(ex);
                throw new ETException("生成多边形KML文件过程出现错误", "KML多边形数据处理", ex);
            }
        }

        /// <summary>
        /// 创建单个KML点位对象
        /// 根据GPS点位数据和对应的名称、备注信息创建KML点位对象
        /// </summary>
        /// <param name="gpsPoint">GPS点位数据，包含经纬度和目标范围</param>
        /// <param name="stationNameRange">站点名称范围</param>
        /// <param name="stationRemarkRange">站点备注范围</param>
        /// <param name="gpsDataRange">GPS数据范围</param>
        /// <returns>创建的KML点位对象</returns>
        XlGpsKmlPoint CreateKmlPoint(XlGpsPointAndRange gpsPoint, Range stationNameRange, Range stationRemarkRange, Range gpsDataRange)
        {
            // 创建新的KML点位对象并设置经纬度
            XlGpsKmlPoint point = new()
            {
                Longitude = gpsPoint.Longitude,
                Latitude = gpsPoint.Latitude
            };

            // 获取当前点位对应的行索引
            int rowIndex = gpsPoint.TargeRange.Row;

            // 设置点位名称
            point.Name = GetPointName(stationNameRange, gpsDataRange, rowIndex, gpsPoint);

            // 如果有备注范围，设置点位描述
            if (stationRemarkRange != null)
            {
                point.Description = GetFormattedDescription(stationRemarkRange, rowIndex);
            }

            return point;
        }

        /// <summary>
        /// 创建单个KML多边形对象
        /// 解析多边形数据文本，提取顶点坐标，创建KML多边形对象
        /// </summary>
        /// <param name="currentGps">当前行的GPS数据</param>
        /// <param name="stationNameRange">站点名称范围</param>
        /// <param name="stationRemarkRange">站点备注范围</param>
        /// <returns>创建的KML多边形对象，如果解析失败则返回null</returns>
        XlGpsKmlPolygon CreateKmlPolygon(Range currentGps, Range stationNameRange, Range stationRemarkRange)
        {
            // 获取当前行的行索引
            int rowIndex = currentGps.Row;

            // 获取多边形文本数据
            string polygonText = currentGps.Value2?.ToString();

            // 如果多边形文本为空，返回null
            if (string.IsNullOrEmpty(polygonText))
            {
                return null;
            }

            // 使用ETPolygonUtil解析多边形数据，转换为几何对象
            Geometry polygon = ETPolygonUtil.ParsePolygonData(polygonText);
            if (polygon == null)
            {
                return null;
            }

            // 提取多边形坐标并转换为KML格式
            string polygonGps = string.Empty;
            if (polygon is NetTopologySuite.Geometries.Polygon polyGeom)
            {
                // 使用外环坐标（多边形的边界）
                Coordinate[] coordinates = polyGeom.ExteriorRing.Coordinates;
                if (coordinates != null && coordinates.Length >= 3)
                {
                    // 格式化坐标为"X,Y,0"的形式，KML中"0"表示高度
                    List<string> formattedCoords = [];
                    foreach (Coordinate coord in coordinates)
                    {
                        formattedCoords.Add($"{coord.X},{coord.Y},0");
                    }
                    // 用空格连接所有坐标点
                    polygonGps = string.Join(" ", formattedCoords);
                }
            }

            // 如果没有提取到有效的多边形坐标，返回null
            if (string.IsNullOrEmpty(polygonGps))
            {
                return null;
            }

            // 创建新的KML多边形对象
            XlGpsKmlPolygon kmlPolygon = new()
            {
                // 设置多边形名称，如果为空则使用"null"
                Name = stationNameRange.Cells[rowIndex, 1].Value?.ToString() ?? "null",
                // 设置多边形坐标
                PolygonGps = polygonGps
            };

            // 如果有备注范围，设置多边形描述
            if (stationRemarkRange != null)
            {
                kmlPolygon.Description = GetFormattedDescription(stationRemarkRange, rowIndex);
            }

            return kmlPolygon;
        }

        /// <summary>
        /// 获取点位名称
        /// 根据不同情况确定点位的名称：
        /// 1. 如果有站点名称范围，使用对应单元格的值
        /// 2. 如果GPS数据范围第一列包含非数值文本，使用经纬度值
        /// 3. 其他情况使用"null"
        /// </summary>
        /// <param name="stationNameRange">站点名称范围</param>
        /// <param name="gpsDataRange">GPS数据范围</param>
        /// <param name="rowIndex">当前行索引</param>
        /// <param name="gpsPoint">GPS点位数据</param>
        /// <returns>点位名称</returns>
        string GetPointName(Range stationNameRange, Range gpsDataRange, int rowIndex, XlGpsPointAndRange gpsPoint)
        {
            // 如果有站点名称范围，优先使用对应单元格的值
            if (stationNameRange != null)
            {
                return ETExcelExtensions.IsCellEmpty(stationNameRange.Cells[rowIndex, 1])
                    ? "null" // 单元格为空时返回"null"
                    : stationNameRange.Cells[rowIndex, 1].Value.ToString();
            }

            // 如果GPS数据范围第一列不为空且不是数值（可能是描述文本）
            if (!ETExcelExtensions.IsCellEmpty(gpsDataRange.Cells[rowIndex, 1]) &&
                !float.TryParse(gpsDataRange.Cells[rowIndex, 1].Value.ToString(), out float _))
            {
                // 使用经纬度坐标作为名称
                return $"{gpsPoint.Longitude},{gpsPoint.Latitude}";
            }

            // 其他情况使用默认名称"null"
            return "null";
        }

        /// <summary>
        /// 获取格式化的描述信息
        /// 从备注范围中获取描述文本，并替换竖线为换行符
        /// </summary>
        /// <param name="remarkRange">备注范围</param>
        /// <param name="rowIndex">当前行索引</param>
        /// <returns>格式化后的描述文本</returns>
        string GetFormattedDescription(Range remarkRange, int rowIndex)
        {
            // 获取备注单元格的值，如果为空则返回"null"
            string description = ETExcelExtensions.IsCellEmpty(remarkRange.Cells[rowIndex, 1])
                ? "null"
                : remarkRange.Cells[rowIndex, 1].Value.ToString();

            // 如果描述为空或只包含空白字符，返回空字符串
            // 否则将描述中的竖线替换为换行符，用于KML文件中格式化显示
            return string.IsNullOrWhiteSpace(description)
                ? string.Empty
                : description.Replace("|", Environment.NewLine);
        }

        /// <summary>
        /// 验证KML文件生成所需的输入参数
        /// 检查KML路径、GPS数据范围是否有效，以及在多边形模式下站点名称是否存在
        /// </summary>
        /// <param name="kmlPath">KML文件路径</param>
        /// <param name="gpsData">GPS数据范围</param>
        /// <param name="requireStationNames">是否要求站点名称（多边形模式下必需）</param>
        void ValidateKmlInputParameters(string kmlPath, Range gpsData, bool requireStationNames)
        {
            // 验证KML路径
            if (string.IsNullOrWhiteSpace(kmlPath))
            {
                throw new ETException("请指定KML文件保存路径", "KML数据处理");
            }

            // 验证GPS数据范围
            if (gpsData == null)
            {
                throw new ETException("GPS数据范围不能为空", "KML数据处理");
            }

            // 在多边形模式下验证站点名称
            if (requireStationNames && ucERS_Point_来源名称.SelectedRange == null)
            {
                throw new ETException("多边形模式下站点名称不能为空", "KML数据处理");
            }
        }

        /// <summary>
        /// 验证数据行数是否超出限制
        /// 如果数据行数超过最大允许值，抛出异常
        /// </summary>
        /// <param name="rowCount">数据行数</param>
        void ValidateDataRowCount(int rowCount)
        {
            if (rowCount > MaxRowCount)
            {
                throw new ETException($"最多支持{MaxRowCount}行数据", "KML数据处理");
            }
        }
        #endregion


    }
}