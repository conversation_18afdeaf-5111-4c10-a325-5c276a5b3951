using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.IO;
using System.Threading;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// PPT辅助操作工具窗体，提供PPT内容替换、生成表格、转换PDF等功能
    /// </summary>
    public partial class frmPPTHelper : Form
    {
        #region 替换PPT关键字功能

        /// <summary>
        /// 根据Excel数据替换PPT中的关键字
        /// </summary>
        void button替换PPT关键字_Click(object sender, EventArgs e)
        {
            ucFileSelectpptTemplatePath.SavePathHistoryToFile();

            // 获取可见范围的数据
            Range dataRange = ucExcelRangeSelectData1.SelectedRange.GetVisibleRange();
            if (dataRange == null)
                return;

            // 根据选项确定操作模式
            HHPowerPoint.PptOperationMode mode = GetOperationMode();

            // 执行替换操作
            HHPowerPoint.ReplaceTextInPptPerRows(
                ucFileSelectpptTemplatePath.Text,
                dataRange,
                textboxSavePath输出路径.Text,
                textBoxProgress,
                mode,
                comboBoxPage处理页面.Text.Trim());

            textBoxProgress.WriteLog($"{DateTime.Now:HH:mm:ss} 完成");
        }

        /// <summary>
        /// 根据用户选择的选项获取PPT操作模式
        /// </summary>
        /// <returns>PPT操作模式</returns>
        HHPowerPoint.PptOperationMode GetOperationMode()
        {
            if (checkBox复制前先把源文本删除.Checked)
                return HHPowerPoint.PptOperationMode.DeleteAndCopyText;

            if (checkBox从模板复制新文本到原PPT.Checked)
                return HHPowerPoint.PptOperationMode.MoveAndCopyText;

            return HHPowerPoint.PptOperationMode.ReplaceText;
        }

        /// <summary>
        /// 处理复制模式选项变更
        /// </summary>
        void checkBox从模板复制新文本到原PPT_CheckedChanged(object sender, EventArgs e)
        {
            if (!checkBox从模板复制新文本到原PPT.Checked)
            {
                checkBox复制前先把源文本删除.Checked = false;
            }

            checkBox复制前先把源文本删除.Enabled = checkBox从模板复制新文本到原PPT.Checked;
        }
        #endregion


        #region 转换为PDF功能
        /// <summary>
        /// 将PPT文件批量转换为PDF
        /// </summary>
        void button执行_转换为PDF_Click(object sender, EventArgs e)
        {
            // 获取文件路径范围
            Range filePathRange = ucERS文件路径_转换为PDF.SelectedRange?.OptimizeRangeSize();

            // 调用PPT转PDF的方法
            PptToPdfPerRowsThread(
                ds输出路径_转换为PDF.Text,
                ds原根路径_转换为PDF.Text,
                filePathRange,
                textBoxProgress,
                textBoxError);
        }

        /// <summary>
        /// 按行处理PPT转PDF的线程方法
        /// </summary>
        /// <param name="outputPath">输出路径</param>
        /// <param name="originalRootPath">原始根路径</param>
        /// <param name="filePathRange">文件路径范围</param>
        /// <param name="textBoxProgress">进度显示文本框</param>
        /// <param name="textBoxError">错误显示文本框</param>
        void PptToPdfPerRowsThread(
            string outputPath,
            string originalRootPath,
            Range filePathRange,
            System.Windows.Forms.TextBox textBoxProgress,
            System.Windows.Forms.TextBox textBoxError)
        {
            if (filePathRange == null)
            {
                textBoxProgress.WriteLog($"{DateTime.Now:HH:mm:ss} 请选择文件路径所在的列");
                return;
            }

            // 在后台线程中执行转换操作
            Thread thread = new(
                () =>
                {
                    try
                    {
                        ProcessFilePathsInRange(filePathRange, outputPath, textBoxProgress, textBoxError);
                        textBoxProgress.WriteLog($"{DateTime.Now:HH:mm:ss} 所有文件处理完成");
                    }
                    catch (Exception ex)
                    {
                        textBoxError.WriteLog($"{DateTime.Now:HH:mm:ss} 转换过程中发生错误: {ex.Message}");
                    }
                });

            thread.Start();
        }

        /// <summary>
        /// 处理范围内的所有文件路径
        /// </summary>
        void ProcessFilePathsInRange(Range filePathRange, string outputPath,
            System.Windows.Forms.TextBox textBoxProgress, System.Windows.Forms.TextBox textBoxError)
        {
            foreach (Range cell in filePathRange.Cells)
            {
                // 跳过隐藏的行
                if (cell.EntireRow.Hidden)
                    continue;

                string filePath = cell.Value2?.ToString();
                if (string.IsNullOrEmpty(filePath))
                    continue;

                try
                {
                    if (!File.Exists(filePath))
                    {
                        // 获取当前行号和标题行行号
                        int currentRowNumber = ((Range)cell.Parent).Row;
                        int headerRowNumber = filePathRange.GetAutoFilterRowNumber();

                        if (currentRowNumber > headerRowNumber)
                        {
                            textBoxProgress.WriteLog($"{DateTime.Now:HH:mm:ss} 找不到文件: {filePath}");
                            continue;
                        }
                    }

                    // 构建输出文件路径
                    string outputFilePath = Path.Combine(
                        outputPath,
                        $"{Path.GetFileNameWithoutExtension(filePath)}.pdf");

                    // 执行PPT转PDF操作
                    ConvertPptToPdf(filePath, outputFilePath, textBoxProgress);

                    textBoxProgress.WriteLog($"{DateTime.Now:HH:mm:ss} 已处理: {filePath}");
                }
                catch (Exception ex)
                {
                    textBoxError.WriteLog(
                        $"{DateTime.Now:HH:mm:ss} 处理文件 {filePath} 时出错: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// 将PPT转换为PDF
        /// </summary>
        /// <param name="inputPath">输入PPT文件路径</param>
        /// <param name="outputPath">输出PDF文件路径</param>
        /// <param name="textBoxProgress">进度显示文本框</param>
        void ConvertPptToPdf(string inputPath, string outputPath, System.Windows.Forms.TextBox textBoxProgress)
        {
            Microsoft.Office.Interop.PowerPoint.Application pptApplication = null;
            Microsoft.Office.Interop.PowerPoint.Presentation pptPresentation = null;

            try
            {
                // 判断PPT文件是否存在
                if (!File.Exists(inputPath))
                {
                    return;
                }

                // 创建PowerPoint应用程序实例
                pptApplication = new Microsoft.Office.Interop.PowerPoint.Application();

                // 打开PPT文件
                pptPresentation = pptApplication.Presentations
                    .Open(
                        inputPath,
                        Microsoft.Office.Core.MsoTriState.msoTrue,  // 只读模式
                        Microsoft.Office.Core.MsoTriState.msoFalse, // 无标题
                        Microsoft.Office.Core.MsoTriState.msoFalse  // 不显示窗口
                    );

                // 转换为PDF
                pptPresentation.ExportAsFixedFormat(
                    outputPath,
                    Microsoft.Office.Interop.PowerPoint.PpFixedFormatType.ppFixedFormatTypePDF);

                textBoxProgress.WriteLog($"{DateTime.Now:HH:mm:ss} 已将 {inputPath} 转换为PDF: {outputPath}");
            }
            catch (Exception ex)
            {
                textBoxProgress.WriteLog($"{DateTime.Now:HH:mm:ss} 转换 {inputPath} 时出错: {ex.Message}");
            }
            finally
            {
                // 清理资源
                if (pptPresentation != null)
                {
                    pptPresentation.Close();
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(pptPresentation);
                }

                if (pptApplication != null)
                {
                    pptApplication.Quit();
                    System.Runtime.InteropServices.Marshal.ReleaseComObject(pptApplication);
                }
            }
        }
        #endregion

        #region 界面控制
        /// <summary>
        /// 构造函数
        /// </summary>
        public frmPPTHelper()
        {
            InitializeComponent();

            // 绑定ComboBox历史记录管理
            ETForm.BindComboBox(comboBoxPage处理页面);
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        void frmHelper_Load(object sender, EventArgs e)
        {
            // 设置初始Excel范围选择
            ucExcelRangeSelectData1.SelectedRange = ETExcelExtensions.GetSelectionRange();

            // 绑定配置设置
            BindConfigurationSettings();
        }

        /// <summary>
        /// 绑定所有控件到配置设置
        /// </summary>
        void BindConfigurationSettings()
        {
            string section = "PPT辅助填写";

            ETForm.BindWindowsFormControl(
                ucFileSelectpptTemplatePath,
                ThisAddIn.ConfigurationSettings,
                section,
                "ucFileSelectpptTemplatePath");

            ETForm.BindWindowsFormControl(
                textboxSavePath输出路径,
                ThisAddIn.ConfigurationSettings,
                section,
                "textboxSavePath输出路径");


            ETForm.BindWindowsFormControl(
                comboBoxPage处理页面,
                ThisAddIn.ConfigurationSettings,
                section,
                "comboBoxPage处理页面");


            ETForm.BindWindowsFormControl(
                ds输出路径_转换为PDF,
                ThisAddIn.ConfigurationSettings,
                section,
                "ds输出路径_转换为PDF");
        }

        /// <summary>
        /// Tab页切换事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 根据不同的Tab页设置splitContainer1的SplitterDistance
        /// </remarks>
        void tabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {
            const int addDistance = 46;

            if (tabControl1.SelectedTab == tabPage替换PPT) // 根据数据表替换PPT
            {
                splitContainer1.SplitterDistance = checkBox从模板复制新文本到原PPT.Top +
                    checkBox从模板复制新文本到原PPT.Height +
                    addDistance;
            }
            else if (tabControl1.SelectedTab == tabPage转换为PDF) // 转换为PDF
            {
                splitContainer1.SplitterDistance = ds原根路径_转换为PDF.Top + ds原根路径_转换为PDF.Height + addDistance;
            }
        }
        #endregion

        #region 文件导入
        /// <summary>
        /// 导入文件清单按钮点击事件
        /// </summary>
        void button导入文件清单_Click(object sender, EventArgs e)
        {
            // 创建并打开文件操作窗体
            frm文件操作 frm = new();
            ThisAddIn.OpenForm(frm);

            // 设置文件操作窗体的初始状态
            frm.tabControl1.SelectedTab = frm.tabPage导入文件名;
            frm.checkBox导入文件_文件.Checked = true;
            frm.checkBox含子目录.Checked = true;
            frm.checkBox导入文件_文件夹.Checked = false;

            // 触发添加文件夹操作
            frm.导入文件_添加文件夹ToolStripMenuItem_Click(null, null);
        }
        #endregion
    }
}