﻿using HyExcelVsto.Extensions;
using ET.Controls;

namespace HyExcelVsto.Module.WX
{
    partial class frm查找站点
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.tabPage查找单个经纬度 = new System.Windows.Forms.TabPage();
            this.checkBox复制到剪贴板 = new System.Windows.Forms.CheckBox();
            this.button选定GPS列 = new System.Windows.Forms.Button();
            this.button查找单个经纬度 = new System.Windows.Forms.Button();
            this.ucERS查找单个经纬度_返回列 = new ET.Controls.ETRangeSelectControl();
            this.listView查找单个经纬度_结果 = new System.Windows.Forms.ListView();
            this.columnHeader值 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader距离 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader行号 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.columnHeader经纬度 = ((System.Windows.Forms.ColumnHeader)(new System.Windows.Forms.ColumnHeader()));
            this.textBox查找单个经纬度 = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage查找站点 = new System.Windows.Forms.TabPage();
            this.checkBox查找站点_增加输出标签 = new System.Windows.Forms.CheckBox();
            this.checkBox查找站点_增加输出距离 = new System.Windows.Forms.CheckBox();
            this.checkBox查找站点_增加输出GPS = new System.Windows.Forms.CheckBox();
            this.checkBox查找站点_增加输出来源名称 = new System.Windows.Forms.CheckBox();
            this.label22 = new System.Windows.Forms.Label();
            this.checkBox执行前清空数据 = new System.Windows.Forms.CheckBox();
            this.numeric查找站点_最小距离 = new System.Windows.Forms.NumericUpDown();
            this.numericUpDown2 = new System.Windows.Forms.NumericUpDown();
            this.numeric查找站点_最大距离 = new System.Windows.Forms.NumericUpDown();
            this.numeric查找站点_最小距离标色 = new System.Windows.Forms.NumericUpDown();
            this.label21 = new System.Windows.Forms.Label();
            this.checkBox提取关键字匹配 = new System.Windows.Forms.CheckBox();
            this.checkBox查找站点_名称相等标色 = new System.Windows.Forms.CheckBox();
            this.label10 = new System.Windows.Forms.Label();
            this.label16 = new System.Windows.Forms.Label();
            this.label查找站点_名称相似填入 = new System.Windows.Forms.Label();
            this.button查找站点_执行 = new System.Windows.Forms.Button();
            this.label9 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label18 = new System.Windows.Forms.Label();
            this.label20 = new System.Windows.Forms.Label();
            this.label26 = new System.Windows.Forms.Label();
            this.label29 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.ucERS查找站点_目标标签 = new ET.Controls.ETRangeSelectControl();
            this.ucERS查找站点_目标GPS = new ET.Controls.ETRangeSelectControl();
            this.ucERS查找站点_来源GPS = new ET.Controls.ETRangeSelectControl();
            this.ucERS查找站点_距离相近填入 = new ET.Controls.ETRangeSelectControl();
            this.ucERS查找站点_名称相似填入 = new ET.Controls.ETRangeSelectControl();
            this.ucERS查找站点_目标 = new ET.Controls.ETRangeSelectControl();
            this.ucERS查找站点_来源 = new ET.Controls.ETRangeSelectControl();
            this.tabPage查找多边形 = new System.Windows.Forms.TabPage();
            this.groupBox查找多边形_查找方式 = new System.Windows.Forms.GroupBox();
            this.radioButton查找多边形_Excel方式 = new System.Windows.Forms.RadioButton();
            this.radioButton查找多边形_KML方式 = new System.Windows.Forms.RadioButton();
            this.etUcFileSelect查找多边形_KML文件路径 = new ET.Controls.ETUcFileSelect();
            this.checkBox查找多边形_执行前清空数据 = new System.Windows.Forms.CheckBox();
            this.label8 = new System.Windows.Forms.Label();
            this.ucERS查找多边形_执行 = new System.Windows.Forms.Button();
            this.label15 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.label19 = new System.Windows.Forms.Label();
            this.label23 = new System.Windows.Forms.Label();
            this.ucERS查找多边形_点位GPS = new ET.Controls.ETRangeSelectControl();
            this.ucERS查找多边形_结果填入 = new ET.Controls.ETRangeSelectControl();
            this.ucERS查找多边形_多边形数据 = new ET.Controls.ETRangeSelectControl();
            this.ucERS查找多边形_多边形名称 = new ET.Controls.ETRangeSelectControl();
            this.panel查找站点_名称相等标色 = new System.Windows.Forms.Panel();
            this.checkBox只匹配目标筛选行 = new System.Windows.Forms.CheckBox();
            this.tabPage查找单个经纬度.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.tabPage查找站点.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numeric查找站点_最小距离)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDown2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numeric查找站点_最大距离)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numeric查找站点_最小距离标色)).BeginInit();
            this.tabPage查找多边形.SuspendLayout();
            this.groupBox查找多边形_查找方式.SuspendLayout();
            this.panel查找站点_名称相等标色.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabPage查找单个经纬度
            // 
            this.tabPage查找单个经纬度.Controls.Add(this.checkBox复制到剪贴板);
            this.tabPage查找单个经纬度.Controls.Add(this.button选定GPS列);
            this.tabPage查找单个经纬度.Controls.Add(this.button查找单个经纬度);
            this.tabPage查找单个经纬度.Controls.Add(this.ucERS查找单个经纬度_返回列);
            this.tabPage查找单个经纬度.Controls.Add(this.listView查找单个经纬度_结果);
            this.tabPage查找单个经纬度.Controls.Add(this.textBox查找单个经纬度);
            this.tabPage查找单个经纬度.Controls.Add(this.label2);
            this.tabPage查找单个经纬度.Controls.Add(this.label1);
            this.tabPage查找单个经纬度.Location = new System.Drawing.Point(4, 22);
            this.tabPage查找单个经纬度.Name = "tabPage查找单个经纬度";
            this.tabPage查找单个经纬度.Size = new System.Drawing.Size(1043, 197);
            this.tabPage查找单个经纬度.TabIndex = 1;
            this.tabPage查找单个经纬度.Text = "查找单个经纬度";
            this.tabPage查找单个经纬度.UseVisualStyleBackColor = true;
            // 
            // checkBox复制到剪贴板
            // 
            this.checkBox复制到剪贴板.AutoSize = true;
            this.checkBox复制到剪贴板.Checked = true;
            this.checkBox复制到剪贴板.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox复制到剪贴板.Location = new System.Drawing.Point(287, 51);
            this.checkBox复制到剪贴板.Name = "checkBox复制到剪贴板";
            this.checkBox复制到剪贴板.Size = new System.Drawing.Size(120, 16);
            this.checkBox复制到剪贴板.TabIndex = 15;
            this.checkBox复制到剪贴板.Text = "自动复制到剪贴板";
            this.checkBox复制到剪贴板.UseVisualStyleBackColor = true;
            // 
            // button选定GPS列
            // 
            this.button选定GPS列.Location = new System.Drawing.Point(470, 46);
            this.button选定GPS列.Name = "button选定GPS列";
            this.button选定GPS列.Size = new System.Drawing.Size(100, 25);
            this.button选定GPS列.TabIndex = 11;
            this.button选定GPS列.Text = "选定GPS列";
            this.button选定GPS列.UseVisualStyleBackColor = true;
            this.button选定GPS列.Click += new System.EventHandler(this.button选定GPS列_Click);
            // 
            // button查找单个经纬度
            // 
            this.button查找单个经纬度.Location = new System.Drawing.Point(470, 12);
            this.button查找单个经纬度.Name = "button查找单个经纬度";
            this.button查找单个经纬度.Size = new System.Drawing.Size(100, 33);
            this.button查找单个经纬度.TabIndex = 11;
            this.button查找单个经纬度.Text = "查找";
            this.button查找单个经纬度.UseVisualStyleBackColor = true;
            this.button查找单个经纬度.Click += new System.EventHandler(this.button查找单个经纬度_Click);
            // 
            // ucERS查找单个经纬度_返回列
            // 
            this.ucERS查找单个经纬度_返回列.EnableEnterThenSelect = false;
            this.ucERS查找单个经纬度_返回列.HideParentForm = true;
            this.ucERS查找单个经纬度_返回列.InputPromptText = "请选择：";
            this.ucERS查找单个经纬度_返回列.Location = new System.Drawing.Point(287, 24);
            this.ucERS查找单个经纬度_返回列.Name = "ucERS查找单个经纬度_返回列";
            this.ucERS查找单个经纬度_返回列.SelectedRange = null;
            this.ucERS查找单个经纬度_返回列.Size = new System.Drawing.Size(177, 21);
            this.ucERS查找单个经纬度_返回列.TabIndex = 13;
            // 
            // listView查找单个经纬度_结果
            // 
            this.listView查找单个经纬度_结果.Columns.AddRange(new System.Windows.Forms.ColumnHeader[] {
            this.columnHeader值,
            this.columnHeader距离,
            this.columnHeader行号,
            this.columnHeader经纬度});
            this.listView查找单个经纬度_结果.FullRowSelect = true;
            this.listView查找单个经纬度_结果.HideSelection = false;
            this.listView查找单个经纬度_结果.Location = new System.Drawing.Point(14, 73);
            this.listView查找单个经纬度_结果.MultiSelect = false;
            this.listView查找单个经纬度_结果.Name = "listView查找单个经纬度_结果";
            this.listView查找单个经纬度_结果.Size = new System.Drawing.Size(556, 193);
            this.listView查找单个经纬度_结果.TabIndex = 9;
            this.listView查找单个经纬度_结果.UseCompatibleStateImageBehavior = false;
            this.listView查找单个经纬度_结果.View = System.Windows.Forms.View.Details;
            this.listView查找单个经纬度_结果.SelectedIndexChanged += new System.EventHandler(this.listView结果_SelectedIndexChanged);
            // 
            // columnHeader值
            // 
            this.columnHeader值.DisplayIndex = 1;
            this.columnHeader值.Text = "值";
            this.columnHeader值.Width = 197;
            // 
            // columnHeader距离
            // 
            this.columnHeader距离.DisplayIndex = 2;
            this.columnHeader距离.Text = "距离";
            this.columnHeader距离.Width = 77;
            // 
            // columnHeader行号
            // 
            this.columnHeader行号.DisplayIndex = 0;
            this.columnHeader行号.Text = "行号";
            this.columnHeader行号.Width = 79;
            // 
            // columnHeader经纬度
            // 
            this.columnHeader经纬度.Text = "经纬度";
            this.columnHeader经纬度.Width = 175;
            // 
            // textBox查找单个经纬度
            // 
            this.textBox查找单个经纬度.Location = new System.Drawing.Point(14, 24);
            this.textBox查找单个经纬度.Multiline = true;
            this.textBox查找单个经纬度.Name = "textBox查找单个经纬度";
            this.textBox查找单个经纬度.Size = new System.Drawing.Size(258, 43);
            this.textBox查找单个经纬度.TabIndex = 8;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(285, 9);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(113, 12);
            this.label2.TabIndex = 12;
            this.label2.Text = "结果\"值\"显示的列：";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(14, 9);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(77, 12);
            this.label1.TabIndex = 10;
            this.label1.Text = "查找经纬度：";
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage查找站点);
            this.tabControl1.Controls.Add(this.tabPage查找单个经纬度);
            this.tabControl1.Controls.Add(this.tabPage查找多边形);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(1051, 223);
            this.tabControl1.TabIndex = 8;
            this.tabControl1.SelectedIndexChanged += new System.EventHandler(this.tabControl1_SelectedIndexChanged);
            // 
            // tabPage查找站点
            // 
            this.tabPage查找站点.Controls.Add(this.panel查找站点_名称相等标色);
            this.tabPage查找站点.Controls.Add(this.checkBox查找站点_增加输出标签);
            this.tabPage查找站点.Controls.Add(this.checkBox查找站点_增加输出距离);
            this.tabPage查找站点.Controls.Add(this.checkBox查找站点_增加输出GPS);
            this.tabPage查找站点.Controls.Add(this.checkBox查找站点_增加输出来源名称);
            this.tabPage查找站点.Controls.Add(this.label22);
            this.tabPage查找站点.Controls.Add(this.checkBox只匹配目标筛选行);
            this.tabPage查找站点.Controls.Add(this.checkBox执行前清空数据);
            this.tabPage查找站点.Controls.Add(this.numeric查找站点_最小距离);
            this.tabPage查找站点.Controls.Add(this.numericUpDown2);
            this.tabPage查找站点.Controls.Add(this.numeric查找站点_最大距离);
            this.tabPage查找站点.Controls.Add(this.numeric查找站点_最小距离标色);
            this.tabPage查找站点.Controls.Add(this.label21);
            this.tabPage查找站点.Controls.Add(this.label10);
            this.tabPage查找站点.Controls.Add(this.label16);
            this.tabPage查找站点.Controls.Add(this.button查找站点_执行);
            this.tabPage查找站点.Controls.Add(this.label9);
            this.tabPage查找站点.Controls.Add(this.label3);
            this.tabPage查找站点.Controls.Add(this.label18);
            this.tabPage查找站点.Controls.Add(this.label20);
            this.tabPage查找站点.Controls.Add(this.label26);
            this.tabPage查找站点.Controls.Add(this.label29);
            this.tabPage查找站点.Controls.Add(this.label12);
            this.tabPage查找站点.Controls.Add(this.ucERS查找站点_目标标签);
            this.tabPage查找站点.Controls.Add(this.ucERS查找站点_目标GPS);
            this.tabPage查找站点.Controls.Add(this.ucERS查找站点_来源GPS);
            this.tabPage查找站点.Controls.Add(this.ucERS查找站点_距离相近填入);
            this.tabPage查找站点.Controls.Add(this.ucERS查找站点_目标);
            this.tabPage查找站点.Controls.Add(this.ucERS查找站点_来源);
            this.tabPage查找站点.Location = new System.Drawing.Point(4, 22);
            this.tabPage查找站点.Name = "tabPage查找站点";
            this.tabPage查找站点.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage查找站点.Size = new System.Drawing.Size(1043, 197);
            this.tabPage查找站点.TabIndex = 2;
            this.tabPage查找站点.Text = "查找站点";
            this.tabPage查找站点.UseVisualStyleBackColor = true;
            // 
            // checkBox查找站点_增加输出标签
            // 
            this.checkBox查找站点_增加输出标签.AutoSize = true;
            this.checkBox查找站点_增加输出标签.Location = new System.Drawing.Point(609, 161);
            this.checkBox查找站点_增加输出标签.Name = "checkBox查找站点_增加输出标签";
            this.checkBox查找站点_增加输出标签.Size = new System.Drawing.Size(48, 16);
            this.checkBox查找站点_增加输出标签.TabIndex = 43;
            this.checkBox查找站点_增加输出标签.Text = "标签";
            this.checkBox查找站点_增加输出标签.UseVisualStyleBackColor = true;
            // 
            // checkBox查找站点_增加输出距离
            // 
            this.checkBox查找站点_增加输出距离.AutoSize = true;
            this.checkBox查找站点_增加输出距离.Location = new System.Drawing.Point(507, 161);
            this.checkBox查找站点_增加输出距离.Name = "checkBox查找站点_增加输出距离";
            this.checkBox查找站点_增加输出距离.Size = new System.Drawing.Size(48, 16);
            this.checkBox查找站点_增加输出距离.TabIndex = 41;
            this.checkBox查找站点_增加输出距离.Text = "距离";
            this.checkBox查找站点_增加输出距离.UseVisualStyleBackColor = true;
            // 
            // checkBox查找站点_增加输出GPS
            // 
            this.checkBox查找站点_增加输出GPS.AutoSize = true;
            this.checkBox查找站点_增加输出GPS.Location = new System.Drawing.Point(561, 161);
            this.checkBox查找站点_增加输出GPS.Name = "checkBox查找站点_增加输出GPS";
            this.checkBox查找站点_增加输出GPS.Size = new System.Drawing.Size(42, 16);
            this.checkBox查找站点_增加输出GPS.TabIndex = 42;
            this.checkBox查找站点_增加输出GPS.Text = "GPS";
            this.checkBox查找站点_增加输出GPS.UseVisualStyleBackColor = true;
            // 
            // checkBox查找站点_增加输出来源名称
            // 
            this.checkBox查找站点_增加输出来源名称.AutoSize = true;
            this.checkBox查找站点_增加输出来源名称.Location = new System.Drawing.Point(429, 161);
            this.checkBox查找站点_增加输出来源名称.Name = "checkBox查找站点_增加输出来源名称";
            this.checkBox查找站点_增加输出来源名称.Size = new System.Drawing.Size(240, 16);
            this.checkBox查找站点_增加输出来源名称.TabIndex = 43;
            this.checkBox查找站点_增加输出来源名称.Text = "来源名称(                          )";
            this.checkBox查找站点_增加输出来源名称.UseVisualStyleBackColor = true;
            // 
            // label22
            // 
            this.label22.AutoSize = true;
            this.label22.Location = new System.Drawing.Point(623, 19);
            this.label22.Name = "label22";
            this.label22.Size = new System.Drawing.Size(227, 12);
            this.label22.TabIndex = 40;
            this.label22.Text = "注:来源仅可见行，但目标来源含隐藏的行";
            // 
            // checkBox执行前清空数据
            // 
            this.checkBox执行前清空数据.AutoSize = true;
            this.checkBox执行前清空数据.Checked = true;
            this.checkBox执行前清空数据.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox执行前清空数据.Location = new System.Drawing.Point(782, 104);
            this.checkBox执行前清空数据.Name = "checkBox执行前清空数据";
            this.checkBox执行前清空数据.Size = new System.Drawing.Size(108, 16);
            this.checkBox执行前清空数据.TabIndex = 39;
            this.checkBox执行前清空数据.Text = "执行前清空数据";
            this.checkBox执行前清空数据.UseVisualStyleBackColor = true;
            // 
            // numeric查找站点_最小距离
            // 
            this.numeric查找站点_最小距离.Location = new System.Drawing.Point(498, 130);
            this.numeric查找站点_最小距离.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.numeric查找站点_最小距离.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            -2147483648});
            this.numeric查找站点_最小距离.Name = "numeric查找站点_最小距离";
            this.numeric查找站点_最小距离.Size = new System.Drawing.Size(63, 21);
            this.numeric查找站点_最小距离.TabIndex = 36;
            this.numeric查找站点_最小距离.Value = new decimal(new int[] {
            1,
            0,
            0,
            -2147483648});
            // 
            // numericUpDown2
            // 
            this.numericUpDown2.Location = new System.Drawing.Point(498, 130);
            this.numericUpDown2.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.numericUpDown2.Name = "numericUpDown2";
            this.numericUpDown2.Size = new System.Drawing.Size(63, 21);
            this.numericUpDown2.TabIndex = 36;
            this.numericUpDown2.Value = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            // 
            // numeric查找站点_最大距离
            // 
            this.numeric查找站点_最大距离.Location = new System.Drawing.Point(612, 130);
            this.numeric查找站点_最大距离.Maximum = new decimal(new int[] {
            1000000,
            0,
            0,
            0});
            this.numeric查找站点_最大距离.Name = "numeric查找站点_最大距离";
            this.numeric查找站点_最大距离.Size = new System.Drawing.Size(63, 21);
            this.numeric查找站点_最大距离.TabIndex = 36;
            this.numeric查找站点_最大距离.Value = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            // 
            // numeric查找站点_最小距离标色
            // 
            this.numeric查找站点_最小距离标色.Location = new System.Drawing.Point(352, 130);
            this.numeric查找站点_最小距离标色.Maximum = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.numeric查找站点_最小距离标色.Name = "numeric查找站点_最小距离标色";
            this.numeric查找站点_最小距离标色.Size = new System.Drawing.Size(59, 21);
            this.numeric查找站点_最小距离标色.TabIndex = 36;
            this.numeric查找站点_最小距离标色.Value = new decimal(new int[] {
            50,
            0,
            0,
            0});
            // 
            // label21
            // 
            this.label21.AutoSize = true;
            this.label21.Location = new System.Drawing.Point(322, 134);
            this.label21.Name = "label21";
            this.label21.Size = new System.Drawing.Size(131, 12);
            this.label21.TabIndex = 38;
            this.label21.Text = "小于            m标色";
            // 
            // checkBox提取关键字匹配
            // 
            this.checkBox提取关键字匹配.AutoSize = true;
            this.checkBox提取关键字匹配.Location = new System.Drawing.Point(428, 17);
            this.checkBox提取关键字匹配.Name = "checkBox提取关键字匹配";
            this.checkBox提取关键字匹配.Size = new System.Drawing.Size(108, 16);
            this.checkBox提取关键字匹配.TabIndex = 34;
            this.checkBox提取关键字匹配.Text = "提取关键字匹配";
            this.checkBox提取关键字匹配.UseVisualStyleBackColor = true;
            // 
            // checkBox查找站点_名称相等标色
            // 
            this.checkBox查找站点_名称相等标色.AutoSize = true;
            this.checkBox查找站点_名称相等标色.Checked = true;
            this.checkBox查找站点_名称相等标色.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox查找站点_名称相等标色.Location = new System.Drawing.Point(317, 17);
            this.checkBox查找站点_名称相等标色.Name = "checkBox查找站点_名称相等标色";
            this.checkBox查找站点_名称相等标色.Size = new System.Drawing.Size(96, 16);
            this.checkBox查找站点_名称相等标色.TabIndex = 34;
            this.checkBox查找站点_名称相等标色.Text = "站名相等标色";
            this.checkBox查找站点_名称相等标色.UseVisualStyleBackColor = true;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(398, 159);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(0, 12);
            this.label10.TabIndex = 27;
            // 
            // label16
            // 
            this.label16.AutoSize = true;
            this.label16.Location = new System.Drawing.Point(17, 134);
            this.label16.Name = "label16";
            this.label16.Size = new System.Drawing.Size(83, 12);
            this.label16.TabIndex = 27;
            this.label16.Text = "距离相近填入:";
            // 
            // label查找站点_名称相似填入
            // 
            this.label查找站点_名称相似填入.AutoSize = true;
            this.label查找站点_名称相似填入.Location = new System.Drawing.Point(11, 19);
            this.label查找站点_名称相似填入.Name = "label查找站点_名称相似填入";
            this.label查找站点_名称相似填入.Size = new System.Drawing.Size(83, 12);
            this.label查找站点_名称相似填入.TabIndex = 28;
            this.label查找站点_名称相似填入.Text = "名称相似填入:";
            // 
            // button查找站点_执行
            // 
            this.button查找站点_执行.Location = new System.Drawing.Point(782, 130);
            this.button查找站点_执行.Name = "button查找站点_执行";
            this.button查找站点_执行.Size = new System.Drawing.Size(129, 47);
            this.button查找站点_执行.TabIndex = 15;
            this.button查找站点_执行.Text = "执  行";
            this.button查找站点_执行.UseVisualStyleBackColor = true;
            this.button查找站点_执行.Click += new System.EventHandler(this.button查找站点_执行_Click);
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(623, 50);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(59, 12);
            this.label9.TabIndex = 12;
            this.label9.Text = "目标标签:";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(322, 163);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(101, 12);
            this.label3.TabIndex = 12;
            this.label3.Text = "增加第三列输出：";
            // 
            // label18
            // 
            this.label18.AutoSize = true;
            this.label18.Location = new System.Drawing.Point(17, 50);
            this.label18.Name = "label18";
            this.label18.Size = new System.Drawing.Size(53, 12);
            this.label18.TabIndex = 12;
            this.label18.Text = "目标GPS:";
            // 
            // label20
            // 
            this.label20.AutoSize = true;
            this.label20.Location = new System.Drawing.Point(17, 23);
            this.label20.Name = "label20";
            this.label20.Size = new System.Drawing.Size(53, 12);
            this.label20.TabIndex = 12;
            this.label20.Text = "来源GPS:";
            // 
            // label26
            // 
            this.label26.AutoSize = true;
            this.label26.Location = new System.Drawing.Point(321, 50);
            this.label26.Name = "label26";
            this.label26.Size = new System.Drawing.Size(59, 12);
            this.label26.TabIndex = 9;
            this.label26.Text = "目标名称:";
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(321, 23);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(59, 12);
            this.label29.TabIndex = 9;
            this.label29.Text = "来源名称:";
            // 
            // label12
            // 
            this.label12.AutoSize = true;
            this.label12.Location = new System.Drawing.Point(468, 134);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(245, 12);
            this.label12.TabIndex = 38;
            this.label12.Text = "小于            m或大于            m舍弃";
            // 
            // ucERS查找站点_目标标签
            // 
            this.ucERS查找站点_目标标签.EnableEnterThenSelect = false;
            this.ucERS查找站点_目标标签.HideParentForm = true;
            this.ucERS查找站点_目标标签.InputPromptText = "请选择：";
            this.ucERS查找站点_目标标签.Location = new System.Drawing.Point(688, 46);
            this.ucERS查找站点_目标标签.Name = "ucERS查找站点_目标标签";
            this.ucERS查找站点_目标标签.SelectedRange = null;
            this.ucERS查找站点_目标标签.Size = new System.Drawing.Size(223, 21);
            this.ucERS查找站点_目标标签.TabIndex = 10;
            // 
            // ucERS查找站点_目标GPS
            // 
            this.ucERS查找站点_目标GPS.EnableEnterThenSelect = false;
            this.ucERS查找站点_目标GPS.HideParentForm = true;
            this.ucERS查找站点_目标GPS.InputPromptText = "请选择：";
            this.ucERS查找站点_目标GPS.Location = new System.Drawing.Point(76, 46);
            this.ucERS查找站点_目标GPS.Name = "ucERS查找站点_目标GPS";
            this.ucERS查找站点_目标GPS.SelectedRange = null;
            this.ucERS查找站点_目标GPS.Size = new System.Drawing.Size(223, 21);
            this.ucERS查找站点_目标GPS.TabIndex = 10;
            this.ucERS查找站点_目标GPS.SelectedEvent += new System.EventHandler(this.ucERS查找站点_目标GPS_SelectedEvent);
            // 
            // ucERS查找站点_来源GPS
            // 
            this.ucERS查找站点_来源GPS.EnableEnterThenSelect = false;
            this.ucERS查找站点_来源GPS.HideParentForm = true;
            this.ucERS查找站点_来源GPS.InputPromptText = "请选择：";
            this.ucERS查找站点_来源GPS.Location = new System.Drawing.Point(78, 19);
            this.ucERS查找站点_来源GPS.Name = "ucERS查找站点_来源GPS";
            this.ucERS查找站点_来源GPS.SelectedRange = null;
            this.ucERS查找站点_来源GPS.Size = new System.Drawing.Size(223, 21);
            this.ucERS查找站点_来源GPS.TabIndex = 10;
            this.ucERS查找站点_来源GPS.SelectedEvent += new System.EventHandler(this.ucERS查找站点_来源GPS_SelectedEvent);
            // 
            // ucERS查找站点_距离相近填入
            // 
            this.ucERS查找站点_距离相近填入.EnableEnterThenSelect = false;
            this.ucERS查找站点_距离相近填入.HideParentForm = true;
            this.ucERS查找站点_距离相近填入.InputPromptText = "请选择：";
            this.ucERS查找站点_距离相近填入.Location = new System.Drawing.Point(106, 130);
            this.ucERS查找站点_距离相近填入.Name = "ucERS查找站点_距离相近填入";
            this.ucERS查找站点_距离相近填入.SelectedRange = null;
            this.ucERS查找站点_距离相近填入.Size = new System.Drawing.Size(195, 21);
            this.ucERS查找站点_距离相近填入.TabIndex = 29;
            // 
            // ucERS查找站点_名称相似填入
            // 
            this.ucERS查找站点_名称相似填入.EnableEnterThenSelect = false;
            this.ucERS查找站点_名称相似填入.HideParentForm = true;
            this.ucERS查找站点_名称相似填入.InputPromptText = "请选择：";
            this.ucERS查找站点_名称相似填入.Location = new System.Drawing.Point(100, 15);
            this.ucERS查找站点_名称相似填入.Name = "ucERS查找站点_名称相似填入";
            this.ucERS查找站点_名称相似填入.SelectedRange = null;
            this.ucERS查找站点_名称相似填入.Size = new System.Drawing.Size(195, 21);
            this.ucERS查找站点_名称相似填入.TabIndex = 30;
            // 
            // ucERS查找站点_目标
            // 
            this.ucERS查找站点_目标.EnableEnterThenSelect = false;
            this.ucERS查找站点_目标.HideParentForm = true;
            this.ucERS查找站点_目标.InputPromptText = "请选择：";
            this.ucERS查找站点_目标.Location = new System.Drawing.Point(382, 46);
            this.ucERS查找站点_目标.Name = "ucERS查找站点_目标";
            this.ucERS查找站点_目标.SelectedRange = null;
            this.ucERS查找站点_目标.Size = new System.Drawing.Size(223, 21);
            this.ucERS查找站点_目标.TabIndex = 11;
            // 
            // ucERS查找站点_来源
            // 
            this.ucERS查找站点_来源.EnableEnterThenSelect = false;
            this.ucERS查找站点_来源.HideParentForm = true;
            this.ucERS查找站点_来源.InputPromptText = "请选择：";
            this.ucERS查找站点_来源.Location = new System.Drawing.Point(382, 19);
            this.ucERS查找站点_来源.Name = "ucERS查找站点_来源";
            this.ucERS查找站点_来源.SelectedRange = null;
            this.ucERS查找站点_来源.Size = new System.Drawing.Size(223, 21);
            this.ucERS查找站点_来源.TabIndex = 11;
            // 
            // tabPage查找多边形
            // 
            this.tabPage查找多边形.Controls.Add(this.groupBox查找多边形_查找方式);
            this.tabPage查找多边形.Controls.Add(this.etUcFileSelect查找多边形_KML文件路径);
            this.tabPage查找多边形.Controls.Add(this.checkBox查找多边形_执行前清空数据);
            this.tabPage查找多边形.Controls.Add(this.label8);
            this.tabPage查找多边形.Controls.Add(this.ucERS查找多边形_执行);
            this.tabPage查找多边形.Controls.Add(this.label15);
            this.tabPage查找多边形.Controls.Add(this.label4);
            this.tabPage查找多边形.Controls.Add(this.label19);
            this.tabPage查找多边形.Controls.Add(this.label23);
            this.tabPage查找多边形.Controls.Add(this.ucERS查找多边形_点位GPS);
            this.tabPage查找多边形.Controls.Add(this.ucERS查找多边形_结果填入);
            this.tabPage查找多边形.Controls.Add(this.ucERS查找多边形_多边形数据);
            this.tabPage查找多边形.Controls.Add(this.ucERS查找多边形_多边形名称);
            this.tabPage查找多边形.Location = new System.Drawing.Point(4, 22);
            this.tabPage查找多边形.Name = "tabPage查找多边形";
            this.tabPage查找多边形.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage查找多边形.Size = new System.Drawing.Size(1043, 197);
            this.tabPage查找多边形.TabIndex = 3;
            this.tabPage查找多边形.Text = "查找多边形";
            this.tabPage查找多边形.UseVisualStyleBackColor = true;
            // 
            // groupBox查找多边形_查找方式
            // 
            this.groupBox查找多边形_查找方式.Controls.Add(this.radioButton查找多边形_Excel方式);
            this.groupBox查找多边形_查找方式.Controls.Add(this.radioButton查找多边形_KML方式);
            this.groupBox查找多边形_查找方式.Location = new System.Drawing.Point(492, 6);
            this.groupBox查找多边形_查找方式.Name = "groupBox查找多边形_查找方式";
            this.groupBox查找多边形_查找方式.Size = new System.Drawing.Size(422, 40);
            this.groupBox查找多边形_查找方式.TabIndex = 42;
            this.groupBox查找多边形_查找方式.TabStop = false;
            this.groupBox查找多边形_查找方式.Text = "查找方式";
            // 
            // radioButton查找多边形_Excel方式
            // 
            this.radioButton查找多边形_Excel方式.AutoSize = true;
            this.radioButton查找多边形_Excel方式.Location = new System.Drawing.Point(200, 17);
            this.radioButton查找多边形_Excel方式.Name = "radioButton查找多边形_Excel方式";
            this.radioButton查找多边形_Excel方式.Size = new System.Drawing.Size(173, 16);
            this.radioButton查找多边形_Excel方式.TabIndex = 1;
            this.radioButton查找多边形_Excel方式.Text = "方式二：在Excel表列中查找";
            this.radioButton查找多边形_Excel方式.UseVisualStyleBackColor = true;
            this.radioButton查找多边形_Excel方式.CheckedChanged += new System.EventHandler(this.radioButton查找多边形_Excel方式_CheckedChanged);
            // 
            // radioButton查找多边形_KML方式
            // 
            this.radioButton查找多边形_KML方式.AutoSize = true;
            this.radioButton查找多边形_KML方式.Checked = true;
            this.radioButton查找多边形_KML方式.Location = new System.Drawing.Point(15, 17);
            this.radioButton查找多边形_KML方式.Name = "radioButton查找多边形_KML方式";
            this.radioButton查找多边形_KML方式.Size = new System.Drawing.Size(161, 16);
            this.radioButton查找多边形_KML方式.TabIndex = 0;
            this.radioButton查找多边形_KML方式.TabStop = true;
            this.radioButton查找多边形_KML方式.Text = "方式一：在KML图层中查找";
            this.radioButton查找多边形_KML方式.UseVisualStyleBackColor = true;
            this.radioButton查找多边形_KML方式.CheckedChanged += new System.EventHandler(this.radioButton查找多边形_KML方式_CheckedChanged);
            // 
            // etUcFileSelect查找多边形_KML文件路径
            // 
            this.etUcFileSelect查找多边形_KML文件路径.AutoFillLatestValue = true;
            this.etUcFileSelect查找多边形_KML文件路径.DefaultFileExtension = "";
            this.etUcFileSelect查找多边形_KML文件路径.FileFilter = "所有文件 (*.*)|*.*";
            this.etUcFileSelect查找多边形_KML文件路径.Location = new System.Drawing.Point(111, 79);
            this.etUcFileSelect查找多边形_KML文件路径.Name = "etUcFileSelect查找多边形_KML文件路径";
            this.etUcFileSelect查找多边形_KML文件路径.Size = new System.Drawing.Size(328, 21);
            this.etUcFileSelect查找多边形_KML文件路径.TabIndex = 41;
            this.etUcFileSelect查找多边形_KML文件路径.UseFolderBrowser = false;
            this.etUcFileSelect查找多边形_KML文件路径.UseOpenFileDialog = true;
            // 
            // checkBox查找多边形_执行前清空数据
            // 
            this.checkBox查找多边形_执行前清空数据.AutoSize = true;
            this.checkBox查找多边形_执行前清空数据.Checked = true;
            this.checkBox查找多边形_执行前清空数据.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox查找多边形_执行前清空数据.Location = new System.Drawing.Point(492, 158);
            this.checkBox查找多边形_执行前清空数据.Name = "checkBox查找多边形_执行前清空数据";
            this.checkBox查找多边形_执行前清空数据.Size = new System.Drawing.Size(108, 16);
            this.checkBox查找多边形_执行前清空数据.TabIndex = 39;
            this.checkBox查找多边形_执行前清空数据.Text = "执行前清空数据";
            this.checkBox查找多边形_执行前清空数据.UseVisualStyleBackColor = true;
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(15, 162);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(59, 12);
            this.label8.TabIndex = 28;
            this.label8.Text = "结果填入:";
            // 
            // ucERS查找多边形_执行
            // 
            this.ucERS查找多边形_执行.Location = new System.Drawing.Point(934, 22);
            this.ucERS查找多边形_执行.Name = "ucERS查找多边形_执行";
            this.ucERS查找多边形_执行.Size = new System.Drawing.Size(120, 105);
            this.ucERS查找多边形_执行.TabIndex = 15;
            this.ucERS查找多边形_执行.Text = "执  行";
            this.ucERS查找多边形_执行.UseVisualStyleBackColor = true;
            this.ucERS查找多边形_执行.Click += new System.EventHandler(this.ucERS查找多边形_执行_Click);
            // 
            // label15
            // 
            this.label15.AutoSize = true;
            this.label15.Location = new System.Drawing.Point(17, 26);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(53, 12);
            this.label15.TabIndex = 12;
            this.label15.Text = "点位GPS:";
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(17, 83);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(77, 12);
            this.label4.TabIndex = 9;
            this.label4.Text = "KML文件路径:";
            // 
            // label19
            // 
            this.label19.AutoSize = true;
            this.label19.Location = new System.Drawing.Point(488, 110);
            this.label19.Name = "label19";
            this.label19.Size = new System.Drawing.Size(71, 12);
            this.label19.TabIndex = 9;
            this.label19.Text = "多边形名称:";
            // 
            // label23
            // 
            this.label23.AutoSize = true;
            this.label23.Location = new System.Drawing.Point(488, 83);
            this.label23.Name = "label23";
            this.label23.Size = new System.Drawing.Size(89, 12);
            this.label23.TabIndex = 9;
            this.label23.Text = "多边形GPS数据:";
            // 
            // ucERS查找多边形_点位GPS
            // 
            this.ucERS查找多边形_点位GPS.EnableEnterThenSelect = false;
            this.ucERS查找多边形_点位GPS.HideParentForm = true;
            this.ucERS查找多边形_点位GPS.InputPromptText = "请选择：";
            this.ucERS查找多边形_点位GPS.Location = new System.Drawing.Point(111, 22);
            this.ucERS查找多边形_点位GPS.Name = "ucERS查找多边形_点位GPS";
            this.ucERS查找多边形_点位GPS.SelectedRange = null;
            this.ucERS查找多边形_点位GPS.Size = new System.Drawing.Size(328, 21);
            this.ucERS查找多边形_点位GPS.TabIndex = 10;
            // 
            // ucERS查找多边形_结果填入
            // 
            this.ucERS查找多边形_结果填入.EnableEnterThenSelect = false;
            this.ucERS查找多边形_结果填入.HideParentForm = true;
            this.ucERS查找多边形_结果填入.InputPromptText = "请选择：";
            this.ucERS查找多边形_结果填入.Location = new System.Drawing.Point(111, 158);
            this.ucERS查找多边形_结果填入.Name = "ucERS查找多边形_结果填入";
            this.ucERS查找多边形_结果填入.SelectedRange = null;
            this.ucERS查找多边形_结果填入.Size = new System.Drawing.Size(328, 21);
            this.ucERS查找多边形_结果填入.TabIndex = 30;
            // 
            // ucERS查找多边形_多边形数据
            // 
            this.ucERS查找多边形_多边形数据.EnableEnterThenSelect = false;
            this.ucERS查找多边形_多边形数据.HideParentForm = true;
            this.ucERS查找多边形_多边形数据.InputPromptText = "请选择：";
            this.ucERS查找多边形_多边形数据.Location = new System.Drawing.Point(586, 79);
            this.ucERS查找多边形_多边形数据.Name = "ucERS查找多边形_多边形数据";
            this.ucERS查找多边形_多边形数据.SelectedRange = null;
            this.ucERS查找多边形_多边形数据.Size = new System.Drawing.Size(328, 21);
            this.ucERS查找多边形_多边形数据.TabIndex = 11;
            // 
            // ucERS查找多边形_多边形名称
            // 
            this.ucERS查找多边形_多边形名称.EnableEnterThenSelect = false;
            this.ucERS查找多边形_多边形名称.HideParentForm = true;
            this.ucERS查找多边形_多边形名称.InputPromptText = "请选择：";
            this.ucERS查找多边形_多边形名称.Location = new System.Drawing.Point(586, 106);
            this.ucERS查找多边形_多边形名称.Name = "ucERS查找多边形_多边形名称";
            this.ucERS查找多边形_多边形名称.SelectedRange = null;
            this.ucERS查找多边形_多边形名称.Size = new System.Drawing.Size(328, 21);
            this.ucERS查找多边形_多边形名称.TabIndex = 11;
            // 
            // panel查找站点_名称相等标色
            // 
            this.panel查找站点_名称相等标色.Controls.Add(this.label查找站点_名称相似填入);
            this.panel查找站点_名称相等标色.Controls.Add(this.ucERS查找站点_名称相似填入);
            this.panel查找站点_名称相等标色.Controls.Add(this.checkBox查找站点_名称相等标色);
            this.panel查找站点_名称相等标色.Controls.Add(this.checkBox提取关键字匹配);
            this.panel查找站点_名称相等标色.Location = new System.Drawing.Point(6, 77);
            this.panel查找站点_名称相等标色.Name = "panel查找站点_名称相等标色";
            this.panel查找站点_名称相等标色.Size = new System.Drawing.Size(707, 47);
            this.panel查找站点_名称相等标色.TabIndex = 44;
            this.panel查找站点_名称相等标色.Visible = false;
            // 
            // checkBox只匹配目标筛选行
            // 
            this.checkBox只匹配目标筛选行.AutoSize = true;
            this.checkBox只匹配目标筛选行.Checked = true;
            this.checkBox只匹配目标筛选行.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox只匹配目标筛选行.Location = new System.Drawing.Point(782, 77);
            this.checkBox只匹配目标筛选行.Name = "checkBox只匹配目标筛选行";
            this.checkBox只匹配目标筛选行.Size = new System.Drawing.Size(120, 16);
            this.checkBox只匹配目标筛选行.TabIndex = 39;
            this.checkBox只匹配目标筛选行.Text = "只匹配目标筛选行";
            this.checkBox只匹配目标筛选行.UseVisualStyleBackColor = true;
            // 
            // frm查找站点
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1051, 223);
            this.Controls.Add(this.tabControl1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frm查找站点";
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "通过经纬度查找";
            this.Load += new System.EventHandler(this.frm通过经纬度查找_Load);
            this.tabPage查找单个经纬度.ResumeLayout(false);
            this.tabPage查找单个经纬度.PerformLayout();
            this.tabControl1.ResumeLayout(false);
            this.tabPage查找站点.ResumeLayout(false);
            this.tabPage查找站点.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numeric查找站点_最小距离)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDown2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numeric查找站点_最大距离)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numeric查找站点_最小距离标色)).EndInit();
            this.tabPage查找多边形.ResumeLayout(false);
            this.tabPage查找多边形.PerformLayout();
            this.groupBox查找多边形_查找方式.ResumeLayout(false);
            this.groupBox查找多边形_查找方式.PerformLayout();
            this.panel查找站点_名称相等标色.ResumeLayout(false);
            this.panel查找站点_名称相等标色.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        public System.Windows.Forms.TabPage tabPage查找单个经纬度;
        private System.Windows.Forms.CheckBox checkBox复制到剪贴板;
        private ETRangeSelectControl ucERS查找单个经纬度_返回列;
        private System.Windows.Forms.Button button查找单个经纬度;
        private System.Windows.Forms.ListView listView查找单个经纬度_结果;
        private System.Windows.Forms.ColumnHeader columnHeader值;
        private System.Windows.Forms.ColumnHeader columnHeader距离;
        private System.Windows.Forms.ColumnHeader columnHeader行号;
        private System.Windows.Forms.ColumnHeader columnHeader经纬度;
        private System.Windows.Forms.TextBox textBox查找单个经纬度;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        public System.Windows.Forms.TabControl tabControl1;
        public System.Windows.Forms.TabPage tabPage查找站点;
        private System.Windows.Forms.CheckBox checkBox查找站点_名称相等标色;
        private ETRangeSelectControl ucERS查找站点_距离相近填入;
        private ETRangeSelectControl ucERS查找站点_名称相似填入;
        private System.Windows.Forms.Label label16;
        private System.Windows.Forms.Label label查找站点_名称相似填入;
        private System.Windows.Forms.Button button查找站点_执行;
        private System.Windows.Forms.Label label18;
        private System.Windows.Forms.Label label20;
        private ETRangeSelectControl ucERS查找站点_目标GPS;
        private ETRangeSelectControl ucERS查找站点_来源GPS;
        private ETRangeSelectControl ucERS查找站点_目标;
        private System.Windows.Forms.Label label26;
        private ETRangeSelectControl ucERS查找站点_来源;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.NumericUpDown numeric查找站点_最小距离标色;
        private System.Windows.Forms.Label label21;
        private System.Windows.Forms.CheckBox checkBox提取关键字匹配;
        private System.Windows.Forms.CheckBox checkBox执行前清空数据;
        private System.Windows.Forms.Label label22;
        private System.Windows.Forms.CheckBox checkBox查找站点_增加输出标签;
        private System.Windows.Forms.CheckBox checkBox查找站点_增加输出距离;
        private System.Windows.Forms.CheckBox checkBox查找站点_增加输出GPS;
        private System.Windows.Forms.CheckBox checkBox查找站点_增加输出来源名称;
        private ETRangeSelectControl ucERS查找站点_目标标签;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.NumericUpDown numeric查找站点_最大距离;
        private System.Windows.Forms.NumericUpDown numeric查找站点_最小距离;
        private System.Windows.Forms.NumericUpDown numericUpDown2;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Button button选定GPS列;
        public System.Windows.Forms.TabPage tabPage查找多边形;
        private System.Windows.Forms.CheckBox checkBox查找多边形_执行前清空数据;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Button ucERS查找多边形_执行;
        private System.Windows.Forms.Label label15;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.Label label19;
        private System.Windows.Forms.Label label23;
        private ETRangeSelectControl ucERS查找多边形_点位GPS;
        private ETRangeSelectControl ucERS查找多边形_结果填入;
        private ETRangeSelectControl ucERS查找多边形_多边形数据;
        private ETRangeSelectControl ucERS查找多边形_多边形名称;
        private System.Windows.Forms.RadioButton radioButton查找多边形_KML方式;
        private System.Windows.Forms.RadioButton radioButton查找多边形_Excel方式;
        private System.Windows.Forms.GroupBox groupBox查找多边形_查找方式;
        private ET.Controls.ETUcFileSelect etUcFileSelect查找多边形_KML文件路径;
        private System.Windows.Forms.Panel panel查找站点_名称相等标色;
        private System.Windows.Forms.CheckBox checkBox只匹配目标筛选行;
    }
}