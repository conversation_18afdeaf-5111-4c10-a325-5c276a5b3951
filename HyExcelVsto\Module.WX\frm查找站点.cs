﻿using ET;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Windows.Forms;

namespace HyExcelVsto.Module.WX
{
    /// <summary>
    /// 站点查找窗体类，提供基于经纬度和名称的站点查找功能
    /// </summary>
    public partial class frm查找站点 : Form
    {
        #region 常量

        /// <summary>
        /// 来源数据最大行数限制
        /// </summary>
        private const int MaxSourceRowCount = 10000;

        /// <summary>
        /// 目标数据最大行数限制
        /// </summary>
        private const int MaxTargetRowCount = 50000;

        /// <summary>
        /// 单次查找最大列数限制
        /// </summary>
        private const int MaxColumnCount = 10;

        #endregion 常量

        #region 辅助方法

        /// <summary>
        /// 检查Range是否非空且包含多个区域
        /// </summary>
        /// <param name="inputRange">要检查的Excel范围</param>
        /// <returns>如果Range非空且Areas数量大于1返回true，否则返回false</returns>
        private bool HasMultipleAreas(Range inputRange)
        {
            return inputRange != null && inputRange.Areas.Count > 1;
        }

        /// <summary>
        /// 验证数据范围的有效性
        /// </summary>
        /// <param name="range">要验证的Excel范围</param>
        /// <param name="maxRows">最大允许行数</param>
        /// <param name="maxColumns">最大允许列数</param>
        /// <param name="operationName">操作名称，用于错误提示</param>
        /// <exception cref="HyException">当验证失败时抛出</exception>
        private void ValidateRange(Range range, int maxRows, int maxColumns, string operationName)
        {
            if (range == null)
            {
                throw new ETException($"未设置{operationName}数据范围", "数据范围验证");
            }

            if (range.Rows.Count > maxRows)
            {
                throw new ETException($"最大支持{maxRows}行{operationName}数据", "数据量验证");
            }

            if (range.Columns.Count > maxColumns)
            {
                throw new ETException($"{operationName}列数不能超过{maxColumns}", "数据范围验证");
            }
        }

        /// <summary>
        /// 验证两个Range是否在同一个工作表中
        /// </summary>
        /// <param name="range1">第一个Excel范围</param>
        /// <param name="range2">第二个Excel范围</param>
        /// <param name="operationName">操作名称，用于错误提示</param>
        /// <exception cref="HyException">当两个范围不在同一个工作表时抛出</exception>
        private void ValidateRangesInSameSheet(Range range1, Range range2, string operationName)
        {
            if (range1 != null && range2 != null && range1.Parent != range2.Parent)
            {
                throw new ETException($"{operationName}和来源表不是同一个工作表", "数据范围验证");
            }
        }

        #endregion 辅助方法

        #region 初始化

        /// <summary>
        /// 初始化站点查找窗体
        /// </summary>
        public frm查找站点()
        {
            try
            {
                InitializeComponent();

                //默认隐藏多边形查找标签页
                tabPage查找多边形.Parent = null;
                panel查找站点_名称相等标色.Visible = false;

                try
                {
                    // 根据权限显是否允许名称查找
                    panel查找站点_名称相等标色.Visible = HyLicenseManager.HasPermission(HyPermissionKeys.Develop)
                        || HyLicenseManager.HasPermission(HyPermissionKeys.WxPro);

                    // 根据权限显示多边形查找标签页
                    tabPage查找多边形.Parent = HyLicenseManager.HasPermission(HyPermissionKeys.Develop) ? tabControl1 : null;
                }
                catch (Exception)
                {
                    //忽略异常
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                throw new ETException("窗体初始化失败", "窗体初始化", ex);
            }
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        private void frm通过经纬度查找_Load(object sender, EventArgs e)
        {
            try
            {
                InitializeTabPage();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show("窗体加载失败", "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 标签页切换事件处理
        /// </summary>
        private void tabControl1_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                InitializeTabPage();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show("切换标签页失败", "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 选定GPS列按钮点击事件处理
        /// </summary>
        private void button选定GPS列_Click(object sender, EventArgs e)
        {
            try
            {
                Range gpsColumns = ETExcelExtensions.FindLatLongColumns(Globals.ThisAddIn.Application.ActiveSheet, 1);
                gpsColumns?.EntireColumn.Activate();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show("选定GPS列失败", "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化当前选中的标签页
        /// </summary>
        public void InitializeTabPage()
        {
            try
            {
                if (tabControl1.SelectedTab == tabPage查找单个经纬度)
                {
                    InitializeSingleGpsSearchTab();
                }
                else if (tabControl1.SelectedTab == tabPage查找站点)
                {
                    InitializeStationSearchTab();
                }
                else if (tabControl1.SelectedTab == tabPage查找多边形)
                {
                    InitializePolygonSearchTab();
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                throw new ETException("初始化标签页失败", "标签页初始化", ex);
            }
        }

        /// <summary>
        /// 初始化单个GPS查找标签页
        /// </summary>
        private void InitializeSingleGpsSearchTab()
        {
            Height = 339;
            Width = 605;

            Range gpsColumns = ETExcelExtensions.FindLatLongColumns(Globals.ThisAddIn.Application.ActiveSheet, 1);
            if (gpsColumns?.Column > 1)
            {
                ucERS查找单个经纬度_返回列.SelectedRange = gpsColumns.Cells[1, 1].Offset[0, -1].EntireColumn;
            }
        }

        /// <summary>
        /// 初始化站点查找标签页
        /// </summary>
        private void InitializeStationSearchTab()
        {
            Height = 251;
            Width = 958;

            Range gpsColumns = ETExcelExtensions.FindLatLongColumns(Globals.ThisAddIn.Application.ActiveSheet, 1);
            if (gpsColumns != null)
            {
                ucERS查找站点_来源GPS.SelectedRange = gpsColumns.EntireColumn;

                if (gpsColumns.Column > 1)
                {
                    ucERS查找站点_来源.SelectedRange = gpsColumns.Cells[1, 1].Offset[0, -1].EntireColumn;
                }
            }
        }

        /// <summary>
        /// 初始化多边形查找标签页
        /// </summary>
        private void InitializePolygonSearchTab()
        {
            Height = 284;
            Width = 1102;

            Range gpsColumns = ETExcelExtensions.FindLatLongColumns(Globals.ThisAddIn.Application.ActiveSheet, 1);
            if (gpsColumns != null)
            {
                ucERS查找多边形_点位GPS.SelectedRange = gpsColumns.EntireColumn;
            }

            // 初始化查找方式控件的可见性
            UpdatePolygonSearchModeVisibility();
        }

        #endregion 初始化

        #region 查找单个经纬度

        /// <summary>
        /// 查找单个经纬度按钮点击事件处理
        /// </summary>
        private void button查找单个经纬度_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查输入并获取要查找的GPS坐标
                string searchText = textBox查找单个经纬度.Text;
                if (string.IsNullOrWhiteSpace(searchText))
                {
                    MessageBox.Show("请输入要查找的GPS坐标", "参数验证", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                XlGpsPoint targetPoint = ETGPS.ExtractGps(searchText);
                if (targetPoint == null)
                {
                    MessageBox.Show("无法解析输入的GPS坐标", "参数验证", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 获取并验证搜索范围
                Range searchRange = ETExcelExtensions.GetSelectionRange()?.GetRangeUnderFilter();
                ValidateSearchRange(searchRange);

                // 执行查找操作
                ExecuteSingleGpsSearch(searchRange, targetPoint);
            }
            catch (ETException ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show(ex.Message, "GPS查找错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show("查找GPS坐标时发生未知错误，请查看日志。", "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 验证搜索范围的有效性
        /// </summary>
        /// <param name="searchRange">要验证的Excel范围</param>
        /// <exception cref="HyException">当验证失败时抛出</exception>
        private void ValidateSearchRange(Range searchRange)
        {
            if (searchRange == null)
            {
                throw new ETException("请选择要搜索的数据范围", "数据范围验证");
            }

            if (searchRange.Cells.Count > 50000 || searchRange.Columns.Count > MaxColumnCount)
            {
                throw new ETException($"选择的范围不正确或过大（最大支持{MaxColumnCount}列）", "数据范围验证");
            }
        }

        /// <summary>
        /// 执行单个GPS坐标的查找操作
        /// </summary>
        /// <param name="searchRange">搜索范围</param>
        /// <param name="targetPoint">目标GPS坐标点</param>
        private void ExecuteSingleGpsSearch(Range searchRange, XlGpsPoint targetPoint)
        {
            // 清除ListView中旧的结果并显示进度提示
            listView查找单个经纬度_结果.Items.Clear();
            ETForm.AddMessageToListview(listView查找单个经纬度_结果, "查找中...");

            // 从searchRange提取多个GPS点
            List<XlGpsPointAndRange> gpsPointsList = ETGPS.ExtractGpsList(searchRange);
            if (gpsPointsList == null || gpsPointsList.Count == 0)
            {
                ETForm.AddMessageToListview(listView查找单个经纬度_结果, "没有找到符合条件的GPS点");
                return;
            }

            // 查找最近的点（限制为100个点）
            const int MaxResultCount = 100;
            List<XlGpsPointAndRange> closestPoints = ETGPS.FindClosestPoints(gpsPointsList, targetPoint, MaxResultCount);

            // 显示查找结果
            DisplaySearchResults(closestPoints, targetPoint);
        }

        /// <summary>
        /// 将查找到的GPS点信息显示到ListView中
        /// </summary>
        /// <param name="closestPoints">最近的GPS点列表</param>
        /// <param name="targetPoint">目标GPS坐标点</param>
        /// <param name="maxCount">最大显示数量，0表示不限制</param>
        public void DisplaySearchResults(List<XlGpsPointAndRange> closestPoints, XlGpsPoint targetPoint, int maxCount = 0)
        {
            if (closestPoints.Count == 0)
            {
                ETForm.AddMessageToListview(listView查找单个经纬度_结果, "未找到就近GPS");
                return;
            }

            listView查找单个经纬度_结果.Items.Clear();
            int displayCount = 0;

            foreach (XlGpsPointAndRange point in closestPoints)
            {
                if (maxCount > 0 && displayCount >= maxCount)
                {
                    break;
                }

                ListViewItem listItem = CreateGpsListViewItem(point, targetPoint);
                listView查找单个经纬度_结果.Items.Add(listItem);
                displayCount++;
            }
        }

        /// <summary>
        /// 创建GPS点的ListView项
        /// </summary>
        /// <param name="point">GPS点信息</param>
        /// <param name="targetPoint">目标GPS坐标点</param>
        /// <returns>创建的ListView项</returns>
        private ListViewItem CreateGpsListViewItem(XlGpsPointAndRange point, XlGpsPoint targetPoint)
        {
            // 计算距离
            double distance = ETGPS.GetDistance(
                point.Latitude,
                point.Longitude,
                targetPoint.Latitude,
                targetPoint.Longitude);

            // 格式化GPS坐标
            string gpsCoordinate = $"{point.Longitude.ToString("#0.00000")},{point.Latitude.ToString("#0.00000")}";

            // 获取显示文本
            string displayText = ucERS查找单个经纬度_返回列.SelectedRange == null
                ? gpsCoordinate
                : ucERS查找单个经纬度_返回列.SelectedRange.GetCellInTargetColumnByLookupRange(point.TargeRange)?.Value?.ToString() ?? gpsCoordinate;

            // 创建ListView项
            ListViewItem listItem = new(displayText);
            listItem.SubItems.Add(distance.ToString("#0"));
            listItem.SubItems.Add(point.TargeRange.Row.ToString(CultureInfo.InvariantCulture));
            listItem.SubItems.Add(gpsCoordinate);
            listItem.ToolTipText = point.TargeRange.Address;

            return listItem;
        }

        /// <summary>
        /// ListView结果选择变更事件处理
        /// </summary>
        private void listView结果_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (listView查找单个经纬度_结果.SelectedItems.Count == 0)
                {
                    return;
                }

                ListViewItem selectedItem = listView查找单个经纬度_结果.SelectedItems[0];
                string gpsCoordinate = selectedItem.SubItems[2].Text;

                // 如果勾选了复制到剪贴板选项且GPS坐标有效
                if (checkBox复制到剪贴板.Checked && !string.IsNullOrWhiteSpace(gpsCoordinate))
                {
                    Clipboard.SetText(gpsCoordinate);
                }

                // 跳转到对应的单元格
                selectedItem.ToolTipText.JumpToAddress();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show("处理选中结果时发生错误，请查看日志。", "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion 查找单个经纬度

        #region 查找站点

        /// <summary>
        /// 执行站点查找操作按钮点击事件处理
        /// </summary>
        private void button查找站点_执行_Click(object sender, EventArgs e)
        {
            try
            {
                // 获取并验证输入范围
                (Range SourceRange, Range SourceGpsRange, Range TargetRange, Range TargetGpsRange, Range TargetLabelRange, Range DistanceFillRange, Range NameFillRange) ranges = GetAndValidateRanges();

                // 获取距离设置
                (double MinDistanceForMark, double MaxDistance, double MinDistance) distances = GetDistanceSettings();

                // 设置Excel快速模式
                ETExcelExtensions.SetAppFastMode();

                try
                {
                    // 执行基于距离的查找
                    if (ranges.DistanceFillRange != null && ranges.DistanceFillRange.Columns.Count >= 2)
                    {
                        ExecuteDistanceBasedSearch(ranges, distances);
                    }

                    // 执行基于名称的查找
                    if (ranges.NameFillRange != null && ranges.NameFillRange.Columns.Count >= 2)
                    {
                        ExecuteNameBasedSearch(ranges);
                    }
                }
                finally
                {
                    ETExcelExtensions.SetAppNormalMode(true);
                }
            }
            catch (ETException ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show(ex.Message, "站点查找错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show("站点查找过程中发生未知错误，请查看日志。", "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 获取并验证所有需要的Excel范围
        /// </summary>
        private (Range SourceRange, Range SourceGpsRange, Range TargetRange, Range TargetGpsRange,
                Range TargetLabelRange, Range DistanceFillRange, Range NameFillRange) GetAndValidateRanges()
        {
            // 获取并优化范围
            Range sourceRange = ucERS查找站点_来源.SelectedRange
                ?.OptimizeRangeSize()
                ?.GetRangeUnderFilter()
                ?.GetVisibleRange();

            Range sourceGpsRange = ucERS查找站点_来源GPS.SelectedRange
                ?.OptimizeRangeSize()
                ?.GetRangeUnderFilter()
                ?.GetVisibleRange() ?? sourceRange;

            Range targetRange = ucERS查找站点_目标.SelectedRange?.OptimizeRangeSize();
            Range targetGpsRange = ucERS查找站点_目标GPS.SelectedRange?.OptimizeRangeSize() ?? targetRange;
            Range targetLabelRange = ucERS查找站点_目标标签.SelectedRange;
            Range distanceFillRange = ucERS查找站点_距离相近填入.SelectedRange;
            Range nameFillRange = ucERS查找站点_名称相似填入.SelectedRange;

            targetRange = checkBox只匹配目标筛选行.Checked ? targetRange.GetVisibleRange() : targetRange;
            targetGpsRange = checkBox只匹配目标筛选行.Checked ? targetGpsRange.GetVisibleRange() : targetGpsRange;

            // 验证范围
            ValidateRangesInSameSheet(distanceFillRange, sourceRange, "距离相近填入");
            ValidateRangesInSameSheet(nameFillRange, sourceRange, "名称相似填入");

            if (sourceGpsRange == null)
            {
                throw new ETException("未设置来源GPS,或者单元格被隐藏", "查找站点-参数检查");
            }

            if (targetGpsRange == null)
            {
                throw new ETException("未设置目标GPS,或者单元格被隐藏", "查找站点-参数检查");
            }

            ValidateRange(sourceGpsRange, MaxSourceRowCount, MaxColumnCount, "来源GPS");
            ValidateRange(targetGpsRange, MaxTargetRowCount, MaxColumnCount, "目标GPS");

            return (sourceRange, sourceGpsRange, targetRange, targetGpsRange,
                    targetLabelRange, distanceFillRange, nameFillRange);
        }

        /// <summary>
        /// 获取距离相关设置
        /// </summary>
        private (double MinDistanceForMark, double MaxDistance, double MinDistance) GetDistanceSettings()
        {
            return (
                Convert.ToDouble(numeric查找站点_最小距离标色.Value),
                Convert.ToDouble(numeric查找站点_最大距离.Value),
                Convert.ToDouble(numeric查找站点_最小距离.Value)
            );
        }

        /// <summary>
        /// 执行基于距离的站点查找
        /// </summary>
        private void ExecuteDistanceBasedSearch(
            (Range SourceRange, Range SourceGpsRange, Range TargetRange, Range TargetGpsRange,
             Range TargetLabelRange, Range DistanceFillRange, Range NameFillRange) ranges,
            (double MinDistanceForMark, double MaxDistance, double MinDistance) distances)
        {
            if (checkBox执行前清空数据.Checked)
            {
                ranges.DistanceFillRange.ClearContents();
            }

            SearchNearestStationsByGps(
                ranges.SourceGpsRange,
                ranges.TargetRange,
                ranges.TargetGpsRange,
                ranges.TargetLabelRange,
                ranges.DistanceFillRange,
                distances.MinDistanceForMark,
                distances.MaxDistance,
                distances.MinDistance
            );
        }

        /// <summary>
        /// 执行基于名称的站点查找
        /// </summary>
        private void ExecuteNameBasedSearch((Range SourceRange, Range SourceGpsRange, Range TargetRange,
            Range TargetGpsRange, Range TargetLabelRange, Range DistanceFillRange, Range NameFillRange) ranges)
        {
            if (checkBox执行前清空数据.Checked)
            {
                ranges.NameFillRange.ClearContents();
            }

            FindBestMatchByName(ranges.SourceRange, ranges.TargetRange, ranges.NameFillRange);
        }

        private void FindBestMatchByName(Range sourceRange, Range targetRange, Range outputRange)
        {
            // 将名称读入到一个列表里
            List<(string Value, string key, int Row)> sourceNames = RangeToStringList(sourceRange);
            HashSet<(string Value, string key)> targetNames = RangeToStringHashSet(targetRange);

            // 准备条件格式的范围集合对象，稍后将用于格式化Excel
            List<Range> conditionalFormatRange = new(sourceNames.Count);

            // 遍历源名称列表
            foreach (var (sourceName, sourceKey, sourceRow) in sourceNames)
            {
                // 使用词典来存储所有可能匹配的目标名称和相应的编辑距离
                Dictionary<string, double> matchDistances = new(targetNames.Count);

                foreach (var (targetName, targetKey) in targetNames)
                {
                    // 计算编辑距离并将其添加到词典
                    ETString.LevenshteinDistance(sourceKey, targetKey, out double distance);
                    matchDistances[targetName] = distance;
                }

                // 根据编辑距离对所有可能的匹配进行排序：距离越小，匹配度越高
                List<KeyValuePair<string, double>> orderedMatches = matchDistances.OrderByDescending(kvp => kvp.Value).ToList();

                // 填充输出范围的列
                int maxColumns = outputRange.Columns.Count;
                for (int outputColIndex = 1; outputColIndex <= maxColumns && outputColIndex <= orderedMatches.Count; outputColIndex++)
                {
                    KeyValuePair<string, double> match = orderedMatches[outputColIndex - 1];
                    Range currentCell = (Range)outputRange.Cells[sourceRow, outputColIndex];

                    // 将排序后的名称按顺序填充到输出范围中
                    currentCell.Value = match.Key;
                    if (sourceName == match.Key)
                    {
                        conditionalFormatRange.Add(currentCell);
                    }
                }
            }

            // 如果有需要格式化的单元格，则应用格式
            if (conditionalFormatRange.Count > 0)
            {
                ETExcelExtensions.Format条件格式警示色(conditionalFormatRange, EnumWarningColor.湖蓝);
            }
        }

        private List<(string Value, string key, int Row)> RangeToStringList(Range range)
        {
            List<(string, string, int)> list = new(range.Rows.Count);
            foreach (Range cell in range.Cells)
            {
                if (ETExcelExtensions.IsCellEmptyOrWhiteSpace(cell)) continue;

                string value0 = cell.Value.ToString();
                string value1 = checkBox提取关键字匹配.Checked ? ETStringPrefixSuffixProcessor.RemovePrefixAndSuffix(value0) : value0;

                if (string.IsNullOrWhiteSpace(value1)) continue;
                list.Add((value0, value1, cell.Row));
            }

            return list;
        }

        private HashSet<(string Value, string key)> RangeToStringHashSet(Range range)
        {
            HashSet<(string, string)> hashSet = new(range.Rows.Count);
            foreach (Range cell in range.Cells)
            {
                if (cell.IsCellEmptyOrWhiteSpace()) continue;

                string value0 = cell.Value.ToString();
                string value1 = checkBox提取关键字匹配.Checked ? ETStringPrefixSuffixProcessor.RemovePrefixAndSuffix(value0) : value0;

                if (string.IsNullOrWhiteSpace(value1)) continue;
                hashSet.Add((value0, value1));
            }

            return hashSet;
        }

        /// <summary>
        /// 基于GPS坐标查找最近的站点
        /// </summary>
        private void SearchNearestStationsByGps(
            Range sourceGpsRange,
            Range targetRange,
            Range targetGpsRange,
            Range targetLabelRange,
            Range outputRange,
            double minimumDistance,
            double maxDistance,
            double minDistance)
        {
            // 提取源GPS点列表
            List<XlGpsPointAndRange> sourceGpsList = ETGPS.ExtractGpsList(sourceGpsRange);
            if (sourceGpsList == null || sourceGpsList.Count == 0)
            {
                throw new ETException("未能从来源范围提取有效的GPS坐标", "GPS数据处理");
            }

            // 提取目标GPS点列表
            List<XlGpsPointAndRange> targetGpsList = ETGPS.ExtractGpsList(targetGpsRange);
            if (targetGpsList == null || targetGpsList.Count == 0)
            {
                throw new ETException("未能从目标范围提取有效的GPS坐标", "GPS数据处理");
            }

            // 检查数据量是否过大
            if (sourceGpsList.Count * targetGpsList.Count > 5000 * 10000)
            {
                throw new ETException($"操作数据量太大({5000 * 10000})", "数据量验证");
            }

            // 判断是否需要输出合并内容
            bool needCombinedOutput = checkBox查找站点_增加输出距离.Checked ||
                                    checkBox查找站点_增加输出GPS.Checked ||
                                    checkBox查找站点_增加输出标签.Checked;

            // 计算每组需要的列数
            int columnsPerGroup = needCombinedOutput ? 3 : 2;

            // 计算要输出的组数
            int groupCount = Math.Max(1, outputRange.Columns.Count / columnsPerGroup);

            // 初始化条件格式化Excel的范围对象
            List<Range> formatRanges = [];

            // 遍历每个源GPS坐标点
            foreach (XlGpsPointAndRange sourceGps in sourceGpsList)
            {
                // 创建一个新的GPS点对象
                XlGpsPoint gps = new() { Latitude = sourceGps.Latitude, Longitude = sourceGps.Longitude };

                // 查找与当前源GPS点最近的多个目标GPS点集合
                List<XlGpsPointAndRange> closestTargets = ETGPS.FindClosestPoints(targetGpsList, gps, groupCount);
                if (closestTargets.Count == 0)
                {
                    continue;
                }

                // 获取源点在输出表中的行索引
                int writeRowIndex = sourceGps.TargeRange.Row;

                // 处理每一组最近的目标点
                for (int groupIndex = 0; groupIndex < Math.Min(groupCount, closestTargets.Count); groupIndex++)
                {
                    XlGpsPointAndRange nearestPoint = closestTargets[groupIndex];
                    double distance = ETGPS.GetDistance(
                        nearestPoint.Latitude,
                        nearestPoint.Longitude,
                        sourceGps.Latitude,
                        sourceGps.Longitude);

                    // 检查距离是否在指定范围内
                    if (distance > maxDistance || distance < minDistance)
                    {
                        continue;
                    }

                    // 计算当前组的起始列
                    int startColumn = groupIndex * columnsPerGroup + 1;

                    // 获取目标点对应的名称 使用相对位置计算目标名称的行号
                    int targetRowOffset = nearestPoint.TargeRange.Row - targetGpsRange.Row + 1;
                    dynamic targetName = targetRange.Cells[targetRowOffset, 1].Value;

                    // 输出目标信息和距离
                    outputRange.Cells[writeRowIndex, startColumn].Value = targetName;
                    outputRange.Cells[writeRowIndex, startColumn + 1].Value = distance.ToString("#0.00");

                    // 若距离小于等于最小标色值，则添加到条件格式化范围
                    if (distance <= minimumDistance)
                    {
                        formatRanges.Add((Range)outputRange.Cells[writeRowIndex, startColumn]);
                    }

                    // 如果需要输出合并内容
                    if (needCombinedOutput)
                    {
                        // 准备输出内容列表
                        List<string> outputList = [];

                        // 如果选中输出距离，添加距离至列表
                        if (checkBox查找站点_增加输出距离.Checked)
                        {
                            outputList.Add($"{distance:0.0}m");
                        }

                        // 如果选中输出GPS坐标，添加坐标至列表
                        if (checkBox查找站点_增加输出GPS.Checked)
                        {
                            outputList.Add($"{nearestPoint.Longitude},{nearestPoint.Latitude}");
                        }

                        // 如果选中输出标签且目标标签范围不为空，添加标签信息至列表
                        if (checkBox查找站点_增加输出标签.Checked && targetLabelRange != null)
                        {
                            dynamic labelValue = targetLabelRange.Cells[targetRowOffset, 1].Value;
                            if (labelValue != null)
                            {
                                outputList.Add(labelValue.ToString());
                            }
                        }

                        // 合并输出内容，并设置到输出单元格
                        if (outputList.Count > 0)
                        {
                            string combinedOutput = string.Join("、", outputList);
                            if (checkBox查找站点_增加输出来源名称.Checked)
                            {
                                combinedOutput = $"{targetName}（{combinedOutput}）";
                            }
                            outputRange.Cells[writeRowIndex, startColumn + 2].Value = combinedOutput;
                        }
                    }
                }
            }

            // 对满足条件的单元格应用湖蓝色警示色格式
            if (formatRanges.Count > 0)
            {
                ETExcelExtensions.Format条件格式警示色(formatRanges, EnumWarningColor.湖蓝);
            }
        }

        /// <summary>
        /// 目标GPS范围选择事件处理
        /// </summary>
        private void ucERS查找站点_目标GPS_SelectedEvent(object sender, EventArgs e)
        {
            if (ucERS查找站点_目标GPS.SelectedRange != null && ucERS查找站点_目标.SelectedRange == null)
            {
                if (ucERS查找站点_目标GPS.SelectedRange != null)
                {
                    ucERS查找站点_目标.SelectedRange = ucERS查找站点_目标GPS.SelectedRange.Offset[0, -1].Columns[1];
                }
            }
        }

        /// <summary>
        /// 来源GPS范围选择事件处理
        /// </summary>
        private void ucERS查找站点_来源GPS_SelectedEvent(object sender, EventArgs e)
        {
            if (ucERS查找站点_来源GPS.SelectedRange != null && ucERS查找站点_来源.SelectedRange == null)
            {
                ucERS查找站点_来源.SelectedRange = ucERS查找站点_来源GPS.SelectedRange.Offset[0, -1].Columns[1];
            }
        }

        #endregion 查找站点

        #region 查找多边形

        /// <summary>
        /// 多边形查找执行按钮点击事件处理
        /// </summary>
        private void ucERS查找多边形_执行_Click(object sender, EventArgs e)
        {
            try
            {
                // 根据用户选择的radioButton确定查找方式
                bool useKmlFile = radioButton查找多边形_KML方式.Checked;

                // 获取并验证输入范围
                Range pointGpsRange = null;
                Range polygonNameRange = null;
                Range polygonDataRange = null;
                Range resultRange = null;

                if (useKmlFile)
                {
                    // 使用KML文件时，需要验证KML文件路径并获取点位GPS范围和结果范围
                    string kmlFilePath = etUcFileSelect查找多边形_KML文件路径.Text;
                    if (string.IsNullOrWhiteSpace(kmlFilePath))
                    {
                        throw new ETException("请选择KML文件路径", "多边形查找-参数检查");
                    }
                    (pointGpsRange, resultRange) = GetAndValidatePolygonRangesForKml();
                }
                else
                {
                    // 不使用KML文件时，使用Excel范围
                    (pointGpsRange, polygonNameRange, polygonDataRange, resultRange) = GetAndValidatePolygonRanges();
                }

                etUcFileSelect查找多边形_KML文件路径.SavePathHistoryToFile();

                // 设置Excel快速模式
                ETExcelExtensions.SetAppFastMode();

                try
                {
                    // 执行多边形查找
                    if (checkBox查找多边形_执行前清空数据.Checked)
                    {
                        resultRange.ClearContents();
                    }

                    if (useKmlFile)
                    {
                        // 从KML文件加载多边形数据并执行查找
                        string kmlFilePath = etUcFileSelect查找多边形_KML文件路径.Text;
                        SearchPointsInPolygonFromKml(pointGpsRange, kmlFilePath, resultRange);
                    }
                    else
                    {
                        // 从Excel范围加载多边形数据并执行查找
                        SearchPointsInPolygon(pointGpsRange, polygonNameRange, polygonDataRange, resultRange);
                    }
                }
                finally
                {
                    ETExcelExtensions.SetAppNormalMode(true);
                }
            }
            catch (ETException ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show(ex.Message, "多边形查找错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show("多边形查找过程中发生未知错误，请查看日志。", "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 获取并验证使用KML文件时的必要Excel范围
        /// </summary>
        private (Range pointGpsRange, Range resultRange) GetAndValidatePolygonRangesForKml()
        {
            // 获取并优化范围
            Range pointGpsRange = ucERS查找多边形_点位GPS.SelectedRange
                ?.OptimizeRangeSize()
                ?.GetRangeUnderFilter();

            Range resultRange = ucERS查找多边形_结果填入.SelectedRange;

            // 验证范围
            if (pointGpsRange == null)
            {
                throw new ETException("未设置点位GPS数据范围", "多边形查找-参数检查");
            }

            if (resultRange == null)
            {
                throw new ETException("未设置结果填入范围", "多边形查找-参数检查");
            }

            // 检查数据量
            ValidateRange(pointGpsRange, MaxSourceRowCount, MaxColumnCount, "点位GPS");

            // 检查结果范围的列数
            if (resultRange.Columns.Count < 2)
            {
                throw new ETException("结果填入范围至少需要2列", "多边形查找-参数检查");
            }

            return (pointGpsRange, resultRange);
        }

        /// <summary>
        /// 获取并验证多边形查找所需的Excel范围
        /// </summary>
        private (Range pointGpsRange, Range polygonNameRange, Range polygonDataRange, Range resultRange) GetAndValidatePolygonRanges()
        {
            // 获取并优化范围
            Range pointGpsRange = ucERS查找多边形_点位GPS.SelectedRange
                ?.OptimizeRangeSize()
                ?.GetRangeUnderFilter();

            Range polygonNameRange = ucERS查找多边形_多边形名称.SelectedRange
                ?.OptimizeRangeSize()
                ?.GetRangeUnderFilter();

            Range polygonDataRange = ucERS查找多边形_多边形数据.SelectedRange
                ?.OptimizeRangeSize()
                ?.GetRangeUnderFilter();

            Range resultRange = ucERS查找多边形_结果填入.SelectedRange;

            // 验证范围
            if (pointGpsRange == null)
            {
                throw new ETException("未设置点位GPS数据范围", "多边形查找-参数检查");
            }

            if (polygonNameRange == null)
            {
                throw new ETException("未设置多边形名称数据范围", "多边形查找-参数检查");
            }

            if (polygonDataRange == null)
            {
                throw new ETException("未设置多边形数据范围", "多边形查找-参数检查");
            }

            if (resultRange == null)
            {
                throw new ETException("未设置结果填入范围", "多边形查找-参数检查");
            }

            // 检查数据量
            ValidateRange(pointGpsRange, MaxSourceRowCount, MaxColumnCount, "点位GPS");
            ValidateRange(polygonNameRange, MaxSourceRowCount, MaxColumnCount, "多边形名称");
            ValidateRange(polygonDataRange, MaxSourceRowCount, MaxColumnCount, "多边形数据");

            // 检查多边形名称和数据的行数是否一致
            if (polygonNameRange.Rows.Count != polygonDataRange.Rows.Count)
            {
                throw new ETException("多边形名称和多边形数据的行数不一致", "多边形查找-参数检查");
            }

            // 检查结果范围的列数
            if (resultRange.Columns.Count < 2)
            {
                throw new ETException("结果填入范围至少需要2列", "多边形查找-参数检查");
            }

            return (pointGpsRange, polygonNameRange, polygonDataRange, resultRange);
        }

        /// <summary>
        /// 执行点是否在多边形内的查找
        /// </summary>
        private void SearchPointsInPolygon(Range pointGpsRange, Range polygonNameRange, Range polygonDataRange, Range resultRange)
        {
            // 提取点坐标
            List<XlGpsPointAndRange> pointsList = ETGPS.ExtractGpsList(pointGpsRange);
            if (pointsList == null || pointsList.Count == 0)
            {
                throw new ETException("未能从点位范围提取有效的GPS坐标", "多边形查找-数据处理");
            }

            // 使用ETPolygonUtil提取多边形数据
            List<(string Name, string PolygonData)> polygons = ETPolygonUtil.ExtractPolygonData(polygonNameRange, polygonDataRange);
            if (polygons.Count == 0)
            {
                throw new ETException("未能提取有效的多边形数据", "多边形查找-数据处理");
            }

            // 检查是否需要输出距离
            bool outputDistance = resultRange.Columns.Count > 2;

            // 创建点列表，准备批量处理
            List<(double Longitude, double Latitude)> points = pointsList
                .Select(p => (p.Longitude, p.Latitude))
                .ToList();

            // 批量处理所有点，避免重复解析多边形
            List<(double Longitude, double Latitude, string Name, double Distance, bool IsInside)> batchResults = ETPolygonUtil.FindNearestPolygonsBatch(points, polygons);

            // 将结果映射回原始点，并填充结果
            for (int i = 0; i < pointsList.Count; i++)
            {
                if (i >= batchResults.Count)
                {
                    continue; // 安全检查
                }

                XlGpsPointAndRange point = pointsList[i];
                (double Longitude, double Latitude, string Name, double Distance, bool IsInside) result = batchResults[i];

                if (string.IsNullOrEmpty(result.Name))
                {
                    continue; // 没有找到匹配的多边形
                }

                // 填充结果
                resultRange.Cells[point.TargeRange.Row, 1].Value = result.IsInside ? "内" : "外";
                resultRange.Cells[point.TargeRange.Row, 2].Value = result.Name;

                if (outputDistance)
                {
                    resultRange.Cells[point.TargeRange.Row, 3].Value = Math.Round(result.Distance, 2);
                }
            }
        }

        /// <summary>
        /// 从KML文件加载多边形数据并执行点位查找
        /// </summary>
        /// <param name="pointGpsRange">点位GPS数据范围</param>
        /// <param name="kmlFilePath">KML文件路径</param>
        /// <param name="resultRange">结果填入范围</param>
        private void SearchPointsInPolygonFromKml(Range pointGpsRange, string kmlFilePath, Range resultRange)
        {
            try
            {
                // 检查KML文件是否存在
                if (!File.Exists(kmlFilePath))
                {
                    throw new ETException($"指定的KML文件不存在: {kmlFilePath}", "多边形查找-KML加载");
                }

                // 加载KML文件中的多边形数据
                List<(string Name, string Wkt)> polygons = ETPolygonUtil.LoadPolygonsFromKmlFile(kmlFilePath);

                if (polygons.Count == 0)
                {
                    throw new ETException("KML文件中未找到有效的多边形数据", "多边形查找-KML加载");
                }

                // 提取点位GPS数据及其行索引
                List<(double longitude, double latitude, int rowIndex)> points = [];
                List<XlGpsPointAndRange> pointsList = ETGPS.ExtractGpsList(pointGpsRange);

                if (pointsList == null || pointsList.Count == 0)
                {
                    throw new ETException("未能从点位范围提取有效的GPS坐标", "多边形查找-数据处理");
                }

                // 保留原始点位的行索引信息
                foreach (XlGpsPointAndRange point in pointsList)
                {
                    points.Add((point.Longitude, point.Latitude, point.TargeRange.Row));
                }

                if (points.Count == 0)
                {
                    throw new ETException("未提取到有效的点位GPS数据", "多边形查找-数据提取");
                }

                // 批量执行多边形查找
                List<(string Name, string Wkt)> polygonsList = polygons.Select(p => (p.Name, p.Wkt)).ToList();

                // 创建不包含行索引的点列表用于查找多边形
                List<(double Longitude, double Latitude)> searchPoints = points.Select(p => (p.longitude, p.latitude)).ToList();
                List<(double Longitude, double Latitude, string Name, double Distance, bool IsInside)> results = ETPolygonUtil.FindNearestPolygonsBatch(searchPoints, polygonsList);

                // 检查结果数量
                if (results.Count != points.Count)
                {
                    throw new ETException("返回的结果数量与点位数量不匹配", "多边形查找-结果处理");
                }

                // 将结果填入结果范围
                bool outputDistance = resultRange.Columns.Count >= 3;

                for (int i = 0; i < points.Count; i++)
                {
                    var (longitude, latitude, rowIndex) = points[i];
                    (double Longitude, double Latitude, string Name, double Distance, bool IsInside) result = results[i];

                    // 使用原始点位的行索引填充结果
                    resultRange.Cells[rowIndex, 1].Value2 = result.IsInside ? "是" : "否";
                    resultRange.Cells[rowIndex, 2].Value2 = result.Name;

                    if (outputDistance)
                    {
                        resultRange.Cells[rowIndex, 3].Value2 = result.Distance;
                    }
                }
            }
            catch (Exception ex) when (!(ex is ETException))
            {
                throw new ETException($"从KML文件查找多边形失败: {ex.Message}", "多边形查找-KML处理", ex);
            }
        }

        /// <summary>
        /// KML方式radioButton选中状态改变事件处理
        /// </summary>
        private void radioButton查找多边形_KML方式_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                UpdatePolygonSearchModeVisibility();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show("切换查找方式失败", "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Excel方式radioButton选中状态改变事件处理
        /// </summary>
        private void radioButton查找多边形_Excel方式_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                UpdatePolygonSearchModeVisibility();
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show("切换查找方式失败", "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 根据选择的查找方式更新控件的可见性
        /// </summary>
        private void UpdatePolygonSearchModeVisibility()
        {
            bool useKmlMode = radioButton查找多边形_KML方式.Checked;

            // KML方式相关控件
            label4.Visible = useKmlMode;  // KML文件路径标签
            etUcFileSelect查找多边形_KML文件路径.Visible = useKmlMode;  // KML文件选择控件

            // Excel方式相关控件
            label23.Visible = !useKmlMode;  // 多边形GPS数据标签
            ucERS查找多边形_多边形数据.Visible = !useKmlMode;  // 多边形GPS数据选择控件
            label19.Visible = !useKmlMode;  // 多边形名称标签
            ucERS查找多边形_多边形名称.Visible = !useKmlMode;  // 多边形名称选择控件
        }

        #endregion 查找多边形
    }
}