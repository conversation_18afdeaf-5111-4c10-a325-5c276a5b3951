using ET;
using HyExcelVsto.Interfaces;
using System;
using System.Collections.Generic;
using System.Windows.Forms;

namespace HyExcelVsto.Module.Common
{
    /// <summary>
    /// 窗体管理类
    /// </summary>
    class FormManager
    {
        internal readonly Dictionary<string, Form> _openForms = [];
        readonly HashSet<IntPtr> _formHandles = [];

        /// <summary>
        /// 打开一个窗体
        /// </summary>
        /// <param name="form">要打开的窗体</param>
        /// <param name="position">窗体位置模式</param>
        /// <param name="singleInstance">是否单实例模式</param>
        public void OpenForm(Form form, XlFormPosition position = XlFormPosition.Center, bool singleInstance = true)
        {
            try
            {
                if (form == null)
                {
                    ETLogManager.Error("OpenForm: 窗体参数为空");
                    throw new ArgumentNullException(nameof(form), "窗体参数不能为空");
                }

                if (form.IsDisposed)
                {
                    ETLogManager.Error($"OpenForm: 窗体 '{form.GetType().Name}' 已被释放");
                    throw new ObjectDisposedException(form.GetType().Name, "窗体已被释放，无法打开");
                }

                // 智能设置窗体标题：只有当窗体标题为空或等于类名时才使用类名作为标题
                string className = form.GetType().Name;
                string currentTitle = form.Text?.Trim() ?? string.Empty;

                // 判断是否需要设置标题：
                // 1. 标题为空
                // 2. 标题等于类名（可能是默认值）
                // 3. 标题等于类名去掉"frm"前缀（可能是之前的逻辑设置的）
                bool shouldSetTitle = string.IsNullOrEmpty(currentTitle) ||
                                    currentTitle == className ||
                                    currentTitle == (className.StartsWith("frm") ? className.Substring(3) : className);

                string formTitle;
                if (shouldSetTitle)
                {
                    // 使用类名作为标题（去掉frm前缀）
                    formTitle = className.StartsWith("frm") ? className.Substring(3) : className;
                    form.Text = formTitle;
                    ETLogManager.Info($"OpenForm: 窗体标题已设置为类名 '{formTitle}'");
                }
                else
                {
                    // 保持现有标题
                    formTitle = currentTitle;
                    ETLogManager.Info($"OpenForm: 保持窗体现有标题 '{formTitle}'");
                }

                ETLogManager.Info($"OpenForm: 准备打开窗体 '{formTitle}'，位置: {position}，单实例: {singleInstance}");

                // 单实例检查
                if (singleInstance && _openForms.ContainsKey(formTitle))
                {
                    Form existingForm = _openForms[formTitle];
                    if (existingForm != null && !existingForm.IsDisposed)
                    {
                        try
                        {
                            existingForm.Activate();
                            existingForm.BringToFront();
                            ETLogManager.Info($"OpenForm: 窗体 '{formTitle}' 已存在，激活现有实例");
                            return;
                        }
                        catch (Exception activateEx)
                        {
                            ETLogManager.Warning($"激活现有窗体 '{formTitle}' 失败: {activateEx.Message}");
                            // 移除无效的窗体引用
                            _openForms.Remove(formTitle);
                        }
                    }
                    else
                    {
                        // 清理无效的窗体引用
                        _openForms.Remove(formTitle);
                        ETLogManager.Info($"OpenForm: 清理无效的窗体引用 '{formTitle}'");
                    }
                }

                // 注册窗体到管理器
                _openForms[formTitle] = form;
                form.FormClosed += OnFormClosed;

                // 设置窗体位置
                SetFormPosition(form, position);

                // 显示窗体
                ShowForm(form, position);

                ETLogManager.Info($"OpenForm: 窗体 '{formTitle}' 打开成功");
            }
            catch (ArgumentNullException argEx)
            {
                ETLogManager.Error($"OpenForm: 参数异常 - {argEx.Message}", argEx);
                throw;
            }
            catch (ObjectDisposedException objEx)
            {
                ETLogManager.Error($"OpenForm: 对象已释放异常 - {objEx.Message}", objEx);
                throw;
            }
            catch (ETException)
            {
                // ETException已经包含详细信息，直接重新抛出
                throw;
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"OpenForm: 打开窗体失败 - {ex.Message}", ex);
                throw new ETException("窗体打开失败", "窗体操作", ex);
            }
        }

        void OnFormClosed(object sender, EventArgs e)
        {
            Form form = (Form)sender;
            _openForms.Remove(form.Text);
            _formHandles.Remove(form.Handle);
        }

        void SetFormPosition(Form form, XlFormPosition position)
        {
            form.Load += position switch
            {
                XlFormPosition.Outside => (s, e) => form.Top = 10000,
                XlFormPosition.Center => (s, e) =>
                {
                    form.Left = Convert.ToInt16((Screen.PrimaryScreen.Bounds.Width - form.Width) / 2);
                    form.Top = Convert.ToInt16((Screen.PrimaryScreen.Bounds.Height - form.Height) / 2);
                }
                ,
                XlFormPosition.Right => (s, e) =>
                {
                    form.Left = Screen.PrimaryScreen.Bounds.Width - form.Width - 50;
                    form.Top = Screen.PrimaryScreen.Bounds.Height - 100 - form.Height;
                }
                ,
                _ => (s, e) => { }
            };
        }

        void ShowForm(Form form, XlFormPosition position)
        {
            try
            {
                if (form.Visible)
                {
                    ETLogManager.Info($"窗体 '{form.Text}' 已经可见，跳过显示操作");
                    return;
                }

                ETLogManager.Info($"开始显示窗体 '{form.Text}'，位置模式: {position}");

                if (position == XlFormPosition.ShowDialog)
                {
                    form.ShowDialog();
                    ETLogManager.Info($"窗体 '{form.Text}' 以对话框模式显示");
                }
                else if (position == XlFormPosition.NoControl)
                {
                    // 增强WPS兼容性处理
                    if (GlobalSettings.IsWps)
                    {
                        try
                        {
                            IntPtr wpsHwnd = new IntPtr(ThisAddIn.ThisAddInInstance.Application.Hwnd);
                            if (WinAPI.IsWindow(wpsHwnd))
                            {
                                form.Show((Form)Control.FromHandle(wpsHwnd));
                                ETLogManager.Info($"窗体 '{form.Text}' 在WPS环境下显示成功");
                            }
                            else
                            {
                                form.Show();
                                ETLogManager.Warning($"WPS窗口句柄无效，窗体 '{form.Text}' 以独立模式显示");
                            }
                        }
                        catch (Exception wpsEx)
                        {
                            ETLogManager.Warning($"WPS环境下显示窗体失败，回退到独立模式: {wpsEx.Message}");
                            form.Show();
                        }
                    }
                    else
                    {
                        // Excel环境处理
                        if (ThisAddIn.TopMostForm != null && !ThisAddIn.TopMostForm.IsDisposed)
                        {
                            form.Show(ThisAddIn.TopMostForm);
                            ETLogManager.Info($"窗体 '{form.Text}' 以TopMostForm为父窗体显示");
                        }
                        else
                        {
                            form.Show();
                            ETLogManager.Warning($"TopMostForm不可用，窗体 '{form.Text}' 以独立模式显示");
                        }
                    }
                    form.StartPosition = FormStartPosition.CenterScreen;
                }
                else
                {
                    // 标准显示模式
                    if (GlobalSettings.IsWps)
                    {
                        try
                        {
                            IntPtr wpsHwnd = new IntPtr(ThisAddIn.ThisAddInInstance.Application.Hwnd);
                            if (WinAPI.IsWindow(wpsHwnd))
                            {
                                form.Show((Form)Control.FromHandle(wpsHwnd));
                                ETLogManager.Info($"窗体 '{form.Text}' 在WPS环境下以标准模式显示");
                            }
                            else
                            {
                                form.Show();
                                ETLogManager.Warning($"WPS窗口句柄无效，窗体 '{form.Text}' 以独立模式显示");
                            }
                        }
                        catch (Exception wpsEx)
                        {
                            ETLogManager.Warning($"WPS环境下显示窗体失败，回退到独立模式: {wpsEx.Message}");
                            form.Show();
                        }
                    }
                    else
                    {
                        // Excel环境处理
                        if (ThisAddIn.TopMostForm != null && !ThisAddIn.TopMostForm.IsDisposed)
                        {
                            form.Show(ThisAddIn.TopMostForm);
                            ETLogManager.Info($"窗体 '{form.Text}' 以TopMostForm为父窗体显示");
                        }
                        else
                        {
                            form.Show();
                            ETLogManager.Warning($"TopMostForm不可用，窗体 '{form.Text}' 以独立模式显示");
                        }
                    }
                }

                _formHandles.Add(form.Handle);
                ETLogManager.Info($"窗体 '{form.Text}' 显示完成，句柄: {form.Handle}");
            }
            catch (System.Runtime.InteropServices.COMException comEx)
            {
                ETLogManager.Error($"显示窗体 '{form?.Text}' 时发生COM异常: {comEx.Message} (错误代码: 0x{comEx.ErrorCode:X})", comEx);
                throw new ETException($"窗体显示COM异常: {comEx.Message}", "窗体操作", comEx);
            }
            catch (System.ComponentModel.Win32Exception win32Ex)
            {
                ETLogManager.Error($"显示窗体 '{form?.Text}' 时发生Win32异常: {win32Ex.Message} (错误代码: {win32Ex.NativeErrorCode})", win32Ex);
                throw new ETException($"窗体显示Win32异常: {win32Ex.Message}", "窗体操作", win32Ex);
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"显示窗体 '{form?.Text}' 时发生异常: {ex.Message}", ex);
                throw new ETException("窗体显示操作失败", "窗体操作", ex);
            }
        }

        /// <summary>
        /// 查找已打开的窗体
        /// </summary>
        public Form FindOpenForm(string formText)
        {
            return _openForms.TryGetValue(formText, out Form form) ? form : null;
        }

        /// <summary>
        /// 向所有已打开的窗体发送Excel选择变更消息
        /// </summary>
        public void NotifyFormsSelectionChange(Microsoft.Office.Interop.Excel.Range target, string message = null)
        {
            foreach (Form form in _openForms.Values)
            {
                if (form is IExcelMessageReceiver receiver)
                {
                    receiver.OnExcelSelectionMessage(target, message);
                }
            }
        }
    }
}