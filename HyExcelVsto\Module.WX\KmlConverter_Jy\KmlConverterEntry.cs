using System;
using System.Windows.Forms;
using ExtensionsTools;

namespace HyExcelVsto.Module.WX.KmlConverter
{
    /// <summary>
    /// KML转换器入口类 - 提供简单的调用接口
    /// </summary>
    public static class KmlConverterEntry
    {
        /// <summary>
        /// 显示KML转换器窗体
        /// </summary>
        public static void ShowKmlConverter()
        {
            try
            {
                ETLogManager.Info("打开KML转换器窗体");

                using (frmKmlConverter form = new frmKmlConverter())
                {
                    form.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"显示KML转换器窗体失败: {ex.Message}", ex);
                MessageBox.Show($"打开KML转换器失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 快速转换KML文件（通过对话框选择）
        /// </summary>
        /// <returns>转换是否成功</returns>
        public static bool QuickConvertKml()
        {
            try
            {
                ETLogManager.Info("启动快速KML转换");

                // 选择源文件
                using (OpenFileDialog openDialog = new OpenFileDialog())
                {
                    openDialog.Title = "选择要转换的KML文件";
                    openDialog.Filter = "KML文件 (*.kml)|*.kml|所有文件 (*.*)|*.*";
                    openDialog.FilterIndex = 1;

                    if (openDialog.ShowDialog() != DialogResult.OK)
                    {
                        return false;
                    }

                    string sourceFile = openDialog.FileName;

                    // 选择目标文件
                    using (SaveFileDialog saveDialog = new SaveFileDialog())
                    {
                        saveDialog.Title = "保存转换后的KML文件";
                        saveDialog.Filter = "KML文件 (*.kml)|*.kml|所有文件 (*.*)|*.*";
                        saveDialog.FilterIndex = 1;

                        // 默认文件名：在原文件名后添加"_converted"
                        string fileName = System.IO.Path.GetFileNameWithoutExtension(sourceFile);
                        string extension = System.IO.Path.GetExtension(sourceFile);
                        saveDialog.FileName = $"{fileName}_converted{extension}";

                        if (saveDialog.ShowDialog() != DialogResult.OK)
                        {
                            return false;
                        }

                        string targetFile = saveDialog.FileName;

                        // 使用窗体进行转换
                        using (frmKmlConverter converter = new frmKmlConverter())
                        {
                            // 这里可以添加程序化转换的逻辑
                            // 目前直接显示窗体让用户操作
                            converter.ShowDialog();
                            return true;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"快速转换KML失败: {ex.Message}", ex);
                MessageBox.Show($"快速转换失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }
    }
}
