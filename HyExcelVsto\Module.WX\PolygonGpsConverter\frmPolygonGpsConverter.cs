using System;
using System.Windows.Forms;
using ET;
using Microsoft.Office.Interop.Excel;

namespace HyExcelVsto.Module.WX.PolygonGpsConverter
{
    /// <summary>
    /// 多边形GPS坐标转换器窗体
    /// 用于将Excel中的GPS坐标数据转换为多边形格式
    /// </summary>
    public partial class frmPolygonGpsConverter : Form
    {
        #region 私有字段

        // 注意：PolygonGpsConverterHelper 是静态类，不需要实例化

        #endregion

        #region 构造函数

        /// <summary>
        /// 初始化多边形GPS坐标转换器窗体
        /// </summary>
        public frmPolygonGpsConverter()
        {
            InitializeComponent();
            InitializeForm();
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化窗体设置
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // 设置窗体基本属性
                this.Text = "多边形GPS坐标转换器";
                this.StartPosition = FormStartPosition.CenterScreen;
                this.FormBorderStyle = FormBorderStyle.FixedDialog;
                this.MaximizeBox = false;
                this.MinimizeBox = false;

                // 设置占位符文本效果
                SetPlaceholderText();

                // 初始化复选框设置
                InitializeCheckBox();

                // 设置窗体图标（如果有的话）
                // this.Icon = Properties.Resources.AppIcon;

                ETLogManager.Info("多边形GPS坐标转换器窗体初始化完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("初始化窗体设置时发生错误", ex);
                MessageBox.Show($"初始化窗体时发生错误：{ex.Message}", "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 初始化复选框设置
        /// </summary>
        private void InitializeCheckBox()
        {
            try
            {
                // 设置复选框默认状态为选中（转换为百度坐标系）
                chkConvertToBaidu.Checked = true;

                // 设置复选框提示信息
                chkConvertToBaidu.Text = "转换为百度坐标系";

                // 添加工具提示
                var toolTip = new ToolTip();
                toolTip.SetToolTip(chkConvertToBaidu,
                    "勾选：将原坐标转换为百度坐标系\r\n" +
                    "不勾选：保留原坐标，仅转换为POLYGON格式");

                ETLogManager.Debug("复选框初始化完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("初始化复选框时发生错误", ex);
            }
        }

        #endregion

        #region 事件处理方法

        /// <summary>
        /// 选择范围按钮点击事件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void btnSelectRange_Click(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info("用户点击选择范围按钮");
                
                // 隐藏窗体以便用户选择Excel范围
                this.WindowState = FormWindowState.Minimized;
                
                // 调用Excel范围选择功能
                string selectedRange = SelectExcelRange();
                
                if (!string.IsNullOrEmpty(selectedRange))
                {
                    txtRangeSelect.Text = selectedRange;
                    txtRangeSelect.ForeColor = System.Drawing.Color.Black; // 确保文本颜色为正常颜色
                    ETLogManager.Info($"用户选择了范围：{selectedRange}");
                }
                
                // 恢复窗体显示
                this.WindowState = FormWindowState.Normal;
                this.BringToFront();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("选择范围时发生错误", ex);
                MessageBox.Show($"选择范围时发生错误：{ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                
                // 确保窗体恢复显示
                this.WindowState = FormWindowState.Normal;
            }
        }

        /// <summary>
        /// 转换按钮点击事件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void btnConvert_Click(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info("用户点击转换按钮");
                
                // 验证输入
                if (string.IsNullOrWhiteSpace(txtRangeSelect.Text) ||
                    txtRangeSelect.Text == "请点击右侧按钮选择Excel范围")
                {
                    MessageBox.Show("请先选择要转换的Excel范围", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 禁用转换按钮，防止重复点击
                btnConvert.Enabled = false;
                btnConvert.Text = "转换中...";

                // 获取Excel应用程序和选中的范围
                var xlApp = Globals.ThisAddIn.Application;
                Range selectedRange = xlApp.Range[txtRangeSelect.Text];

                // 获取复选框状态，决定是否转换为百度坐标系
                bool convertToBaidu = chkConvertToBaidu.Checked;

                // 执行坐标转换
                PolygonConversionResult result = PolygonGpsConverterHelper.ProcessPolygonConversion(selectedRange, convertToBaidu);

                // 显示转换结果，包含转换模式信息
                string modeInfo = convertToBaidu ? "转换模式：原坐标 → 百度坐标系" : "转换模式：保留原坐标，仅格式化为POLYGON";
                txtResult.Text = $"{modeInfo}\r\n{result.GetSummary()}";

                ETLogManager.Info($"GPS坐标转换完成 - {modeInfo}");
                string successMessage = convertToBaidu ?
                    "GPS坐标转换完成！已转换为百度坐标系。" :
                    "GPS坐标格式化完成！已转换为POLYGON格式，坐标系保持不变。";
                MessageBox.Show(successMessage, "成功",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                ETLogManager.Error("转换GPS坐标时发生错误", ex);
                MessageBox.Show($"转换GPS坐标时发生错误：{ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // 恢复转换按钮状态
                btnConvert.Enabled = true;
                btnConvert.Text = "开始转换";
            }
        }

        /// <summary>
        /// 复制结果按钮点击事件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void btnCopyResult_Click(object sender, EventArgs e)
        {
            try
            {
                if (!string.IsNullOrWhiteSpace(txtResult.Text))
                {
                    Clipboard.SetText(txtResult.Text);
                    ETLogManager.Info("转换结果已复制到剪贴板");
                    MessageBox.Show("转换结果已复制到剪贴板", "成功", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("没有可复制的转换结果", "提示", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("复制结果时发生错误", ex);
                MessageBox.Show($"复制结果时发生错误：{ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 设置占位符文本效果
        /// </summary>
        private void SetPlaceholderText()
        {
            try
            {
                // 设置初始占位符文本
                txtRangeSelect.Text = "请点击右侧按钮选择Excel范围";
                txtRangeSelect.ForeColor = System.Drawing.Color.Gray;

                // 添加焦点事件处理
                txtRangeSelect.Enter += TxtRangeSelect_Enter;
                txtRangeSelect.Leave += TxtRangeSelect_Leave;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("设置占位符文本时发生错误", ex);
            }
        }

        /// <summary>
        /// 范围选择文本框获得焦点事件
        /// </summary>
        private void TxtRangeSelect_Enter(object sender, EventArgs e)
        {
            if (txtRangeSelect.Text == "请点击右侧按钮选择Excel范围")
            {
                txtRangeSelect.Text = "";
                txtRangeSelect.ForeColor = System.Drawing.Color.Black;
            }
        }

        /// <summary>
        /// 范围选择文本框失去焦点事件
        /// </summary>
        private void TxtRangeSelect_Leave(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtRangeSelect.Text))
            {
                txtRangeSelect.Text = "请点击右侧按钮选择Excel范围";
                txtRangeSelect.ForeColor = System.Drawing.Color.Gray;
            }
        }

        /// <summary>
        /// 选择Excel范围
        /// </summary>
        /// <returns>选择的范围地址</returns>
        private string SelectExcelRange()
        {
            try
            {
                // 调用Excel API让用户选择范围
                var xlApp = Globals.ThisAddIn.Application;

                // 使用InputBox让用户选择范围
                object selectedRange = xlApp.InputBox(
                    "请选择包含GPS坐标的范围",
                    "选择范围",
                    Type: 8); // Type 8 表示Range对象

                if (selectedRange is Range range)
                {
                    return range.Address;
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                ETLogManager.Error("选择Excel范围时发生错误", ex);
                throw;
            }
        }

        #endregion

        #region 窗体事件

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void frmPolygonGpsConverter_Load(object sender, EventArgs e)
        {
            try
            {
                ETLogManager.Info("多边形GPS坐标转换器窗体加载完成");
                
                // 设置默认焦点
                txtRangeSelect.Focus();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("窗体加载时发生错误", ex);
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        private void frmPolygonGpsConverter_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                ETLogManager.Info("多边形GPS坐标转换器窗体正在关闭");

                // 静态类不需要清理资源
            }
            catch (Exception ex)
            {
                ETLogManager.Error("窗体关闭时发生错误", ex);
            }
        }

        #endregion
    }
}
