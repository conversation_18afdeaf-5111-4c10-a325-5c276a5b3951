using Microsoft.Office.Core;
using Microsoft.Office.Interop.PowerPoint;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading;
using Application = Microsoft.Office.Interop.PowerPoint.Application;
using Shape = Microsoft.Office.Interop.PowerPoint.Shape;
using TextBox = System.Windows.Forms.TextBox;

namespace ET
{
    public class HHPowerPoint
    {
        /// <summary>
        /// 删除位于PowerPoint幻灯片之外的所有对象。
        /// </summary>
        /// <param name="pptFilePaths">要处理的PPT文件路径集合</param>
        /// <param name="savePath">保存修改后的PPT的路径</param>
        /// <param name="specifiedPages">指定需要修改的页码范围</param>
        /// <param name="textBoxLog">用于记录日志的TextBox对象</param>
        /// <remarks>
        /// 此方法会遍历指定的PPT文件，删除位于幻灯片边界外的所有对象，并将修改后的文件保存到指定路径。
        /// </remarks>
        public static void DeleteShapesOutsideSlides(IEnumerable<string> pptFilePaths, string savePath,
            string specifiedPages, TextBox textBoxLog)
        {
            Application pptApplication = new();

            try
            {
                foreach (string pptFilePath in pptFilePaths)
                {
                    if (string.IsNullOrEmpty(pptFilePath))
                    {
                        textBoxLog.AppendText("提供的PPT文件路径为空。\r\n");
                        continue;
                    }

                    Presentation presentation = null;

                    try
                    {
                        if (!File.Exists(pptFilePath)) continue;
                        // 打开PPT文件，不显示窗口
                        presentation = pptApplication.Presentations.Open(pptFilePath, WithWindow: MsoTriState.msoFalse);
                        HashSet<int> pagesToModify = GetPagesToModify(presentation.Slides.Count, specifiedPages);

                        foreach (Slide slide in presentation.Slides)
                        {
                            if (!pagesToModify.Contains(slide.SlideIndex)) continue;

                            // 从后往前遍历Shapes集合，避免删除对象时索引变化
                            for (int i = slide.Shapes.Count; i >= 1; i--)
                            {
                                Shape shape = slide.Shapes[i];

                                if (IsShapeOutsideSlide(shape, slide))
                                {
                                    shape.Delete();
                                }
                            }
                        }

                        // 保存修改后的PPT文件
                        string saveFileName = Path.GetFileName(pptFilePath);
                        string fullSavePath = Path.Combine(savePath, saveFileName);
                        presentation.SaveAs(fullSavePath, PpSaveAsFileType.ppSaveAsDefault, MsoTriState.msoFalse);
                    }
                    catch (COMException ex)
                    {
                        textBoxLog.AppendText($"在处理文件 '{pptFilePath}' 时发生错误: {ex.Message}\r\n");
                    }
                    finally
                    {
                        if (presentation != null)
                        {
                            presentation.Close();
                            Marshal.ReleaseComObject(presentation);
                        }
                    }
                }
            }
            finally
            {
                pptApplication.Quit();
                Marshal.ReleaseComObject(pptApplication);
            }
        }

        /// <summary>
        /// 判断形状是否位于幻灯片外部
        /// </summary>
        /// <param name="shape">要检查的形状</param>
        /// <param name="slide">形状所在的幻灯片</param>
        /// <returns>如果形状位于幻灯片外部，则返回true；否则返回false</returns>
        static bool IsShapeOutsideSlide(Shape shape, Slide slide)
        {
            return shape.Left < 0 || shape.Left > slide.Master.Width ||
                   shape.Top < 0 || shape.Top > slide.Master.Height;
        }

        /// <summary>
        /// 将Excel指定范围的数据插入到PowerPoint中。
        /// </summary>
        /// <param name="dataRange">待处理的Excel数据范围。</param>
        /// <param name="savePath">处理后PowerPoint文件的保存路径。</param>
        /// <param name="textBoxLog">用于记录日志信息的文本框控件。</param>
        /// <remarks>
        /// 此方法会读取Excel中的数据，并将其插入到指定的PowerPoint文件中。
        /// </remarks>
        public static void InsertExcelRangeToPpt(Microsoft.Office.Interop.Excel.Range dataRange, string savePath, TextBox textBoxLog)
        {
            object[,] dataArray = dataRange.GetVisibleCellsValueAsArray();
            Application pptApp = new();
            Presentation presentation = null;
            Slide slide = null;
            string lastPptPath = null;

            try
            {
                for (int currentRowIndex = 1; currentRowIndex < dataArray.GetLength(0); currentRowIndex++)
                {
                    string pptPath = dataArray[currentRowIndex, 0]?.ToString();
                    if (string.IsNullOrEmpty(pptPath) || pptPath == lastPptPath) continue;

                    lastPptPath = pptPath;
                    // 打开PPT文件，不显示窗口
                    presentation = pptApp.Presentations.Open(pptPath, WithWindow: MsoTriState.msoFalse);

                    if (!TryParsePositionInfo(dataArray[currentRowIndex, 1]?.ToString(), presentation, out (float Top, float Left, float Width, float Height, int SlideIndex) positionInfo))
                    {
                        textBoxLog.AppendTextInvoke($"第{currentRowIndex}行位置信息格式书写错误");
                        continue;
                    }

                    slide = presentation.Slides[positionInfo.SlideIndex];

                    int contentRowCount = CountContentRows(dataArray, currentRowIndex, pptPath);

                    InsertTableToSlide(slide, dataArray, currentRowIndex, contentRowCount, positionInfo);

                    // 保存修改后的PPT文件
                    presentation.SaveAs(Path.Combine(savePath, Path.GetFileName(pptPath)));
                    presentation.Close();
                    Marshal.ReleaseComObject(slide);
                    Marshal.ReleaseComObject(presentation);
                }
            }
            catch (Exception ex)
            {
                textBoxLog.AppendTextInvoke($"Error: {ex.Message}");
            }
            finally
            {
                pptApp.Quit();
                Marshal.ReleaseComObject(pptApp);
            }
        }

        /// <summary>
        /// 尝试解析位置信息
        /// </summary>
        /// <param name="positionString">位置信息字符串</param>
        /// <param name="presentation">当前处理的PPT演示文稿</param>
        /// <param name="positionInfo">解析后的位置信息</param>
        /// <returns>如果解析成功返回true，否则返回false</returns>
        static bool TryParsePositionInfo(string positionString, Presentation presentation, out (float Top, float Left, float Width, float Height, int SlideIndex) positionInfo)
        {
            positionInfo = default;
            if (string.IsNullOrEmpty(positionString)) return false;

            string[] position = positionString.Split(',');
            if (position.Length != 5) return false;

            try
            {
                // 解析位置信息，将百分比转换为实际尺寸
                positionInfo = (
                    Top: float.Parse(position[0].TrimEnd('%'), CultureInfo.InvariantCulture) / 100 * presentation.PageSetup.SlideHeight,
                    Left: float.Parse(position[1].TrimEnd('%'), CultureInfo.InvariantCulture) / 100 * presentation.PageSetup.SlideWidth,
                    Width: float.Parse(position[2].TrimEnd('%'), CultureInfo.InvariantCulture) / 100 * presentation.PageSetup.SlideWidth,
                    Height: float.Parse(position[3].TrimEnd('%'), CultureInfo.InvariantCulture) / 100 * presentation.PageSetup.SlideHeight,
                    SlideIndex: int.Parse(position[4])
                );
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 计算需要添加到PPT的数据行数
        /// </summary>
        /// <param name="dataArray">数据数组</param>
        /// <param name="currentRowIndex">当前行索引</param>
        /// <param name="pptPath">当前处理的PPT路径</param>
        /// <returns>需要添加的数据行数</returns>
        static int CountContentRows(object[,] dataArray, int currentRowIndex, string pptPath)
        {
            int contentRowCount = 1;
            int nextRowIndex = currentRowIndex;
            // 计算连续的相同PPT路径的行数
            while (nextRowIndex < dataArray.GetLength(0) - 1 &&
                   (string.IsNullOrEmpty(dataArray[nextRowIndex + 1, 0]?.ToString()) ||
                    dataArray[nextRowIndex + 1, 0].ToString() == pptPath))
            {
                contentRowCount++;
                nextRowIndex++;
            }
            return contentRowCount;
        }

        /// <summary>
        /// 在幻灯片上插入表格并填充数据
        /// </summary>
        /// <param name="slide">目标幻灯片</param>
        /// <param name="dataArray">数据数组</param>
        /// <param name="currentRowIndex">当前行索引</param>
        /// <param name="contentRowCount">内容行数</param>
        /// <param name="positionInfo">位置信息</param>
        static void InsertTableToSlide(Slide slide, object[,] dataArray, int currentRowIndex, int contentRowCount, (float Top, float Left, float Width, float Height, int SlideIndex) positionInfo)
        {
            // 添加表格到幻灯片
            Shape tableShape = slide.Shapes.AddTable(contentRowCount + 1, dataArray.GetLength(1) - 2,
                positionInfo.Left, positionInfo.Top, positionInfo.Width, positionInfo.Height);
            Table table = tableShape.Table;

            // 填充表格数据
            for (int col = 3; col <= dataArray.GetLength(1); col++)
            {
                SetCellContent(table.Cell(1, col - 2), dataArray[1, col - 1]?.ToString() ?? string.Empty);

                for (int i = 0; i < contentRowCount; i++)
                {
                    SetCellContent(table.Cell(i + 2, col - 2), dataArray[currentRowIndex + i, col - 1]?.ToString() ?? string.Empty);
                }
            }
        }

        /// <summary>
        /// 设置单元格内容
        /// </summary>
        /// <param name="cell">目标单元格</param>
        /// <param name="content">要设置的内容</param>
        static void SetCellContent(Cell cell, string content)
        {
            cell.Shape.TextFrame.TextRange.Text = content;
        }

        /// <summary>
        /// 删除指定页面的文本和表格。
        /// </summary>
        /// <param name="targetPpt">目标PPT。</param>
        /// <param name="specifiedPages">指定的页面，页面之间用逗号分隔的字符串。</param>
        /// <param name="textBoxLog">日志输出的文本框。</param>
        public static void DeleteTextAndTable(Presentation targetPpt, string specifiedPages, TextBox textBoxLog)
        {
            try
            {
                textBoxLog.AppendTextInvoke("    正在删除原有文本和表格对象");

                HashSet<int> pagesToModify = GetPagesToModify(targetPpt.Slides.Count, specifiedPages);

                // 遍历目标PPT的每一张幻灯片
                for (int i = 1; i <= targetPpt.Slides.Count; i++)
                {
                    if (!pagesToModify.Contains(i)) continue;

                    Slide targetSlide = targetPpt.Slides[i];
                    Microsoft.Office.Interop.PowerPoint.Shapes shapes = targetSlide.Shapes;

                    // 从后往前遍历Shapes集合，这样可以在删除过程中避免索引混乱
                    for (int j = shapes.Count; j >= 1; j--)
                    {
                        Shape shape = shapes[j];

                        // 删除文本框、表格和特定类型的占位符
                        if (ShouldDeleteShape(shape))
                        {
                            shape.Delete();
                        }
                    }
                }

                textBoxLog.AppendTextInvoke("    操作完成", false);
            }
            catch (Exception ex)
            {
                textBoxLog.AppendTextInvoke($"删除文本和表格时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 判断是否应该删除形状
        /// </summary>
        /// <param name="shape">要判断的形状</param>
        /// <returns>如果应该删除返回true，否则返回false</returns>
        static bool ShouldDeleteShape(Shape shape)
        {
            if (shape.Type == MsoShapeType.msoPlaceholder)
            {
                return shape.PlaceholderFormat.Type == PpPlaceholderType.ppPlaceholderTitle ||
                       shape.PlaceholderFormat.Type == PpPlaceholderType.ppPlaceholderCenterTitle ||
                       shape.PlaceholderFormat.Type == PpPlaceholderType.ppPlaceholderSubtitle;
            }
            return shape.HasTextFrame == MsoTriState.msoTrue || shape.HasTable == MsoTriState.msoTrue;
        }

        /// <summary>
        /// 在文本范围内替换文本
        /// </summary>
        /// <param name="textRange">要操作的文本范围</param>
        /// <param name="headerText">要查找的文本</param>
        /// <param name="replaceText">替换的文本</param>
        static void ReplaceTextInRange(TextRange textRange, string headerText, string replaceText)
        {
            int foundIndex = textRange.Text.IndexOf(headerText, StringComparison.OrdinalIgnoreCase);

            while (foundIndex != -1)
            {
                TextRange rangeToReplace = textRange.Characters(foundIndex + 1, headerText.Length);
                rangeToReplace.Text = replaceText;

                // 避免无限循环，需要重新设置搜索起点
                foundIndex = textRange.Text.IndexOf(headerText, foundIndex + replaceText.Length,
                    StringComparison.OrdinalIgnoreCase);
            }
        }

        /// <summary>
        /// 获取需要修改的页面集合
        /// </summary>
        /// <param name="slidesCount">幻灯片总数</param>
        /// <param name="specifiedPages">指定的页面范围字符串</param>
        /// <returns>需要修改的页面索引集合</returns>
        static HashSet<int> GetPagesToModify(int slidesCount, string specifiedPages)
        {
            HashSet<int> pagesToModify = [];

            if (specifiedPages.Equals("all", StringComparison.InvariantCultureIgnoreCase))
            {
                for (int i = 1; i <= slidesCount; i++) pagesToModify.Add(i);
            }
            else
            {
                string[] pages = specifiedPages.Split(new[] { ',', '，' }, StringSplitOptions.RemoveEmptyEntries);
                foreach (string page in pages)
                    if (int.TryParse(page.Trim(), out int pageIndex))
                        pagesToModify.Add(pageIndex);
            }

            return pagesToModify;
        }


        #region 批量根据表头关键字替换ppt内文本（用于根据表格内容生成ppt)

        /// <summary>
        /// 根据Excel数据批量替换PPT中的文本
        /// </summary>
        /// <param name="pptTemplatePath">PPT模板路径</param>
        /// <param name="dataRange">Excel数据范围</param>
        /// <param name="savePath">保存路径</param>
        /// <param name="textBoxLog">日志输出控件</param>
        /// <param name="mode">操作模式</param>
        /// <param name="specifiedPages">指定页面</param>
        /// <remarks>
        /// 该方法根据Excel表格中的数据，批量替换PPT中的文本。
        /// 可以根据不同的操作模式执行不同的操作，如替换文本、移动并复制文本、删除并复制文本等。
        /// </remarks>
        public static void ReplaceTextInPptPerRows(string pptTemplatePath, Microsoft.Office.Interop.Excel.Range dataRange, string savePath,
            TextBox textBoxLog, PptOperationMode mode, string specifiedPages)
        {
            // 验证输入参数
            if (string.IsNullOrEmpty(savePath) || !Directory.Exists(savePath))
            {
                textBoxLog.AppendTextInvoke("保存目录不存在");
                return;
            }
            if (dataRange == null)
            {
                textBoxLog.AppendTextInvoke("数据来源错误");
                return;
            }

            // 初始化 PowerPoint 应用程序对象
            Application pptApplication = new();
            Presentations presentations = pptApplication.Presentations;

            // 获取可见单元格数据
            object[,] visibleCellsData = dataRange.GetVisibleCellsValueAsArray();

            // 获取表头行
            object[] headerRow = Enumerable.Range(0, visibleCellsData.GetLength(1))
                .Select(col => visibleCellsData[0, col])
                .ToArray();

            // 处理每一行数据
            for (int rowIndex = 1; rowIndex < visibleCellsData.GetLength(0); rowIndex++)
            {
                string pptFileName = Convert.ToString(visibleCellsData[rowIndex, 0]);
                if (string.IsNullOrWhiteSpace(pptFileName)) continue;

                string pptFilePath = File.Exists(pptFileName) ? pptFileName : Path.Combine(savePath, pptFileName);

                Presentation presentation = null;

                try
                {
                    // 打开或创建PPT文件
                    presentation = OpenOrCreatePresentation(pptFilePath, pptTemplatePath, presentations, textBoxLog);
                    if (presentation == null) continue;

                    // 根据模式执行操作
                    ExecuteOperationBasedOnMode(mode, presentation, pptTemplatePath, specifiedPages, textBoxLog);

                    // 替换文本
                    ReplaceTextInPresentation(presentation, headerRow, visibleCellsData, rowIndex, specifiedPages, textBoxLog);

                    // 保存PPT
                    SavePresentation(presentation, pptFilePath, savePath, textBoxLog);
                }
                finally
                {
                    // 关闭演示文稿
                    presentation?.Close();
                }
            }

            // 退出 PowerPoint 应用程序
            pptApplication.Quit();
        }

        /// <summary>
        /// 打开或创建PPT演示文稿
        /// </summary>
        /// <param name="pptFilePath">PPT文件路径</param>
        /// <param name="pptTemplatePath">PPT模板路径</param>
        /// <param name="presentations">Presentations集合</param>
        /// <param name="textBoxLog">日志输出控件</param>
        /// <returns>打开或创建的Presentation对象</returns>
        static Presentation OpenOrCreatePresentation(string pptFilePath, string pptTemplatePath, Presentations presentations, TextBox textBoxLog)
        {
            if (File.Exists(pptFilePath))
            {
                textBoxLog.AppendTextInvoke($"正在打开文件：{pptFilePath}");
                return presentations.Open(pptFilePath);
            }
            else if (!string.IsNullOrWhiteSpace(pptTemplatePath) && File.Exists(pptTemplatePath))
            {
                textBoxLog.AppendTextInvoke($"从模板创建 PPT 文件：{pptTemplatePath}");
                return presentations.Open(pptTemplatePath);
            }
            else
            {
                textBoxLog.AppendTextInvoke($"无法找到PPT文件或模板：{pptFilePath}");
                return null;
            }
        }

        /// <summary>
        /// 根据操作模式执行相应的操作
        /// </summary>
        /// <param name="mode">操作模式</param>
        /// <param name="presentation">目标演示文稿</param>
        /// <param name="pptTemplatePath">PPT模板路径</param>
        /// <param name="specifiedPages">指定页面</param>
        /// <param name="textBoxLog">日志输出控件</param>
        static void ExecuteOperationBasedOnMode(PptOperationMode mode, Presentation presentation, string pptTemplatePath, string specifiedPages, TextBox textBoxLog)
        {
            if (string.IsNullOrEmpty(pptTemplatePath)) return;

            Presentation templatePpt = presentation.Application.Presentations.Open(pptTemplatePath);

            try
            {
                switch (mode)
                {
                    case PptOperationMode.MoveAndCopyText:
                        MoveTextAndTable(presentation, specifiedPages, textBoxLog);
                        CopyTextAndTable(templatePpt, presentation, specifiedPages, textBoxLog);
                        break;
                    case PptOperationMode.DeleteAndCopyText:
                        DeleteTextAndTable(presentation, specifiedPages, textBoxLog);
                        DeleteTextAndTable(presentation, specifiedPages, textBoxLog);
                        CopyTextAndTable(templatePpt, presentation, specifiedPages, textBoxLog);
                        break;
                }
            }
            finally
            {
                templatePpt.Close();
            }
        }

        /// <summary>
        /// 在演示文稿中替换文本
        /// </summary>
        /// <param name="presentation">目标演示文稿</param>
        /// <param name="headerRow">表头行</param>
        /// <param name="visibleCellsData">可见单元格数据</param>
        /// <param name="rowIndex">当前行索引</param>
        /// <param name="specifiedPages">指定页面</param>
        /// <param name="textBoxLog">日志输出控件</param>
        static void ReplaceTextInPresentation(Presentation presentation, object[] headerRow, object[,] visibleCellsData, int rowIndex, string specifiedPages, TextBox textBoxLog)
        {
            textBoxLog.AppendTextInvoke("    正在替换关键字");
            for (int colIndex = 0; colIndex < headerRow.Length; colIndex++)
            {
                string headerText = headerRow[colIndex]?.ToString();
                string replaceText = visibleCellsData[rowIndex, colIndex]?.ToString();
                ReplaceTextInPresentation(presentation, specifiedPages, headerText, replaceText, textBoxLog);
            }
            textBoxLog.AppendTextInvoke("    操作完成", false);
        }

        /// <summary>
        /// 保存演示文稿
        /// </summary>
        /// <param name="presentation">要保存的演示文稿</param>
        /// <param name="pptFilePath">PPT文件路径</param>
        /// <param name="savePath">保存路径</param>
        /// <param name="textBoxLog">日志输出控件</param>
        static void SavePresentation(Presentation presentation, string pptFilePath, string savePath, TextBox textBoxLog)
        {
            string saveFileName = Path.GetFileName(pptFilePath);
            string fullSavePath = Path.Combine(savePath, saveFileName);
            presentation.SaveAs(fullSavePath, PpSaveAsFileType.ppSaveAsDefault, MsoTriState.msoFalse);
            textBoxLog.AppendTextInvoke($"    文件已保存：{fullSavePath}");
        }

        /// <summary>
        /// 在演示文稿中替换文本
        /// </summary>
        /// <param name="presentation">目标演示文稿</param>
        /// <param name="specifiedPages">指定页面</param>
        /// <param name="headerText">要替换的文本</param>
        /// <param name="replaceText">替换后的文本</param>
        /// <param name="textBoxLog">日志输出控件</param>
        public static void ReplaceTextInPresentation(Presentation presentation, string specifiedPages, string headerText,
            string replaceText, TextBox textBoxLog)
        {
            if (presentation == null || headerText == null || replaceText == null) return;

            HashSet<int> pagesToModify = GetPagesToModify(presentation.Slides.Count, specifiedPages);

            foreach (Slide slide in presentation.Slides)
            {
                if (!pagesToModify.Contains(slide.SlideIndex)) continue;

                foreach (Shape shape in slide.Shapes)
                {
                    if (shape.HasTextFrame == MsoTriState.msoTrue)
                    {
                        // 替换形状中的文本
                        ReplaceTextInRange(shape.TextFrame.TextRange, headerText, replaceText);
                    }
                    else if (shape.HasTable == MsoTriState.msoTrue)
                    {
                        // 遍历表格中的每个单元格
                        for (int row = 1; row <= shape.Table.Rows.Count; row++)
                        {
                            for (int col = 1; col <= shape.Table.Columns.Count; col++)
                            {
                                Cell cell = shape.Table.Cell(row, col);
                                if (cell.Shape.HasTextFrame == MsoTriState.msoTrue)
                                {
                                    // 替换表格单元格中的文本
                                    ReplaceTextInRange(cell.Shape.TextFrame.TextRange, headerText, replaceText);
                                }
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 移动指定页面中的文本和表格到PPT页面右侧（页面之外）
        /// </summary>
        /// <param name="targetPpt">目标PPT文件</param>
        /// <param name="specifiedPages">指定的幻灯片页面集合，格式为逗号分隔的字符串</param>
        /// <param name="textBoxLog">用于记录日志的文本框</param>
        public static void MoveTextAndTable(Presentation targetPpt, string specifiedPages, TextBox textBoxLog)
        {
            try
            {
                textBoxLog.AppendTextInvoke("    正在移动文本和表格到PPT页面右侧（页面之外）");

                HashSet<int> pagesToModify = GetPagesToModify(targetPpt.Slides.Count, specifiedPages);

                foreach (Slide slide in targetPpt.Slides)
                {
                    if (!pagesToModify.Contains(slide.SlideIndex)) continue;

                    foreach (Shape shape in slide.Shapes)
                    {
                        if (shape.HasTextFrame == MsoTriState.msoTrue || shape.HasTable == MsoTriState.msoTrue)
                        {
                            // 移动形状的位置到幻灯片右侧
                            shape.Left += targetPpt.PageSetup.SlideWidth * 1.2f;
                        }
                    }
                }

                textBoxLog.AppendTextInvoke("    操作完成", false);
            }
            catch (Exception ex)
            {
                textBoxLog.AppendTextInvoke($"移动文本和表格时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 复制指定页面的文本和表格
        /// </summary>
        /// <param name="templatePpt">模板PPT</param>
        /// <param name="targetPpt">目标PPT</param>
        /// <param name="specifiedPages">指定的页面，页面之间用逗号分隔的字符串</param>
        /// <param name="textBoxLog">日志输出的文本框</param>
        public static void CopyTextAndTable(Presentation templatePpt, Presentation targetPpt, string specifiedPages,
            TextBox textBoxLog)
        {
            try
            {
                textBoxLog.AppendTextInvoke("    正在复制模板文本和表格对象");

                HashSet<int> pagesToModify = GetPagesToModify(targetPpt.Slides.Count, specifiedPages);

                for (int i = 1; i <= templatePpt.Slides.Count; i++)
                {
                    if (!pagesToModify.Contains(i)) continue;

                    Slide templateSlide = templatePpt.Slides[i];
                    Slide targetSlide = targetPpt.Slides[i];

                    foreach (Shape shape in templateSlide.Shapes)
                    {
                        if (shape.HasTextFrame == MsoTriState.msoTrue || shape.HasTable == MsoTriState.msoTrue)
                        {
                            shape.Copy();
                            Thread.Sleep(300); // 暂停300毫秒以确保复制过程完成
                            targetSlide.Shapes.Paste();
                        }
                    }
                }

                textBoxLog.AppendTextInvoke("    操作完成", false);
            }
            catch (Exception ex)
            {
                textBoxLog.AppendTextInvoke($"复制文本和表格时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// PPT操作模式枚举
        /// </summary>
        public enum PptOperationMode
        {
            ReplaceText,     // 替换文本
            MoveAndCopyText, // 移动并复制文本
            DeleteAndCopyText // 删除并复制文本
        }

        #endregion 批量根据表头关键字替换ppt内文本（用于根据表格内容生成ppt)




    }
}