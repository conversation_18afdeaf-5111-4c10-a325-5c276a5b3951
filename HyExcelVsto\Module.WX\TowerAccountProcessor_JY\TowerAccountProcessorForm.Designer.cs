using System;
using System.Drawing;
using System.Windows.Forms;
using ET.Controls;

namespace HyExcelVsto.Module.WX.TowerAccountProcessor_JY
{
    partial class TowerAccountProcessorForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (components != null)
                {
                    components.Dispose();
                }
                ReleaseCustomResources();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.lblTowerAccountDescription = new System.Windows.Forms.Label();
            this.lblTowerAccountStep1 = new System.Windows.Forms.Label();
            this.btnExtractTelecomPart = new System.Windows.Forms.Button();
            this.lblTowerAccountStep2 = new System.Windows.Forms.Label();
            this.btnSummarizeToAuditAccount = new System.Windows.Forms.Button();
            this.lblTowerAccountNote = new System.Windows.Forms.Label();
            this.etLogDisplayControlTower = new ET.Controls.ETLogDisplayControl();
            this.SuspendLayout();
            // 
            // lblTowerAccountDescription
            // 
            this.lblTowerAccountDescription.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lblTowerAccountDescription.Location = new System.Drawing.Point(20, 15);
            this.lblTowerAccountDescription.Name = "lblTowerAccountDescription";
            this.lblTowerAccountDescription.Size = new System.Drawing.Size(550, 30);
            this.lblTowerAccountDescription.TabIndex = 0;
            this.lblTowerAccountDescription.Text = "铁塔内部台账梳理功能：用于处理铁塔设计内部台账，提取电信部分数据并汇总到会审台账";
            // 
            // lblTowerAccountStep1
            // 
            this.lblTowerAccountStep1.AutoSize = true;
            this.lblTowerAccountStep1.Location = new System.Drawing.Point(20, 55);
            this.lblTowerAccountStep1.Name = "lblTowerAccountStep1";
            this.lblTowerAccountStep1.Size = new System.Drawing.Size(23, 12);
            this.lblTowerAccountStep1.TabIndex = 1;
            this.lblTowerAccountStep1.Text = "1、";
            // 
            // btnExtractTelecomPart
            // 
            this.btnExtractTelecomPart.Location = new System.Drawing.Point(50, 50);
            this.btnExtractTelecomPart.Name = "btnExtractTelecomPart";
            this.btnExtractTelecomPart.Size = new System.Drawing.Size(200, 35);
            this.btnExtractTelecomPart.TabIndex = 2;
            this.btnExtractTelecomPart.Text = "铁塔内部台账提取电信部分";
            this.btnExtractTelecomPart.UseVisualStyleBackColor = true;
            this.btnExtractTelecomPart.Click += new System.EventHandler(this.BtnExtractTelecomPart_Click);
            // 
            // lblTowerAccountStep2
            // 
            this.lblTowerAccountStep2.AutoSize = true;
            this.lblTowerAccountStep2.Location = new System.Drawing.Point(20, 100);
            this.lblTowerAccountStep2.Name = "lblTowerAccountStep2";
            this.lblTowerAccountStep2.Size = new System.Drawing.Size(23, 12);
            this.lblTowerAccountStep2.TabIndex = 3;
            this.lblTowerAccountStep2.Text = "2、";
            // 
            // btnSummarizeToAuditAccount
            // 
            this.btnSummarizeToAuditAccount.Location = new System.Drawing.Point(50, 95);
            this.btnSummarizeToAuditAccount.Name = "btnSummarizeToAuditAccount";
            this.btnSummarizeToAuditAccount.Size = new System.Drawing.Size(200, 35);
            this.btnSummarizeToAuditAccount.TabIndex = 4;
            this.btnSummarizeToAuditAccount.Text = "汇总新铁塔台账到铁塔会审台账";
            this.btnSummarizeToAuditAccount.UseVisualStyleBackColor = true;
            this.btnSummarizeToAuditAccount.Click += new System.EventHandler(this.BtnSummarizeToAuditAccount_Click);
            // 
            // lblTowerAccountNote
            // 
            this.lblTowerAccountNote.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lblTowerAccountNote.Location = new System.Drawing.Point(20, 145);
            this.lblTowerAccountNote.Name = "lblTowerAccountNote";
            this.lblTowerAccountNote.Size = new System.Drawing.Size(550, 15);
            this.lblTowerAccountNote.TabIndex = 5;
            this.lblTowerAccountNote.Text = "注：本页针对性强，代码是硬编码，如需改动需直接修改代码";
            // 
            // etLogDisplayControlTower
            // 
            this.etLogDisplayControlTower.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.etLogDisplayControlTower.AutoScrollToBottom = true;
            this.etLogDisplayControlTower.CurrentLogLevel = ET.Controls.ETLogDisplayControl.LogLevel.Info;
            this.etLogDisplayControlTower.CustomInitialMessage = null;
            this.etLogDisplayControlTower.Location = new System.Drawing.Point(20, 170);
            this.etLogDisplayControlTower.LogBackColor = System.Drawing.Color.White;
            this.etLogDisplayControlTower.LogFont = new System.Drawing.Font("Consolas", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.etLogDisplayControlTower.LogForeColor = System.Drawing.Color.Black;
            this.etLogDisplayControlTower.Margin = new System.Windows.Forms.Padding(2);
            this.etLogDisplayControlTower.MaxLogLines = 1000;
            this.etLogDisplayControlTower.Name = "etLogDisplayControlTower";
            this.etLogDisplayControlTower.ShowInitialMessage = true;
            this.etLogDisplayControlTower.ShowLogLevel = true;
            this.etLogDisplayControlTower.ShowTimestamp = true;
            this.etLogDisplayControlTower.Size = new System.Drawing.Size(550, 290);
            this.etLogDisplayControlTower.TabIndex = 6;
            this.etLogDisplayControlTower.WordWrap = true;
            // 
            // TowerAccountProcessorForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(584, 472);
            this.Controls.Add(this.lblTowerAccountDescription);
            this.Controls.Add(this.lblTowerAccountStep1);
            this.Controls.Add(this.btnExtractTelecomPart);
            this.Controls.Add(this.lblTowerAccountStep2);
            this.Controls.Add(this.btnSummarizeToAuditAccount);
            this.Controls.Add(this.lblTowerAccountNote);
            this.Controls.Add(this.etLogDisplayControlTower);
            this.Margin = new System.Windows.Forms.Padding(2);
            this.MinimumSize = new System.Drawing.Size(600, 510);
            this.Name = "TowerAccountProcessorForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "铁塔内部台账转换工具";
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private Label lblTowerAccountDescription;
        private Label lblTowerAccountStep1;
        private Label lblTowerAccountStep2;
        private Label lblTowerAccountNote;
        private Button btnExtractTelecomPart;
        private Button btnSummarizeToAuditAccount;
        private ET.Controls.ETLogDisplayControl etLogDisplayControlTower;
    }
}
