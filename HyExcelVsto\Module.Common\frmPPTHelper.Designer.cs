﻿using ET.Controls;

namespace HyExcelVsto.Module.Common
{
    partial class frmPPTHelper
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.label1 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.button替换PPT关键字 = new System.Windows.Forms.Button();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage替换PPT = new System.Windows.Forms.TabPage();
            this.textboxSavePath输出路径 = new ET.Controls.HHUcDirectorySelect();
            this.comboBoxPage处理页面 = new System.Windows.Forms.ComboBox();
            this.ucFileSelectpptTemplatePath = new ET.Controls.ETUcFileSelect();
            this.ucExcelRangeSelectData1 = new ET.Controls.ETRangeSelectControl();
            this.checkBox复制前先把源文本删除 = new System.Windows.Forms.CheckBox();
            this.checkBox从模板复制新文本到原PPT = new System.Windows.Forms.CheckBox();
            this.label8 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.label3 = new System.Windows.Forms.Label();
            this.label4 = new System.Windows.Forms.Label();
            this.tabPage转换为PDF = new System.Windows.Forms.TabPage();
            this.button导入文件清单 = new System.Windows.Forms.Button();
            this.ds原根路径_转换为PDF = new ET.Controls.HHUcDirectorySelect();
            this.ds输出路径_转换为PDF = new ET.Controls.HHUcDirectorySelect();
            this.label42 = new System.Windows.Forms.Label();
            this.label29 = new System.Windows.Forms.Label();
            this.button执行_转换为PDF = new System.Windows.Forms.Button();
            this.label30 = new System.Windows.Forms.Label();
            this.ucERS文件路径_转换为PDF = new ET.Controls.ETRangeSelectControl();
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.splitContainer2 = new System.Windows.Forms.SplitContainer();
            this.textBoxError = new System.Windows.Forms.TextBox();
            this.labelError = new System.Windows.Forms.Label();
            this.textBoxProgress = new System.Windows.Forms.TextBox();
            this.labelProgress = new System.Windows.Forms.Label();
            this.tabControl1.SuspendLayout();
            this.tabPage替换PPT.SuspendLayout();
            this.tabPage转换为PDF.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).BeginInit();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer2)).BeginInit();
            this.splitContainer2.Panel1.SuspendLayout();
            this.splitContainer2.Panel2.SuspendLayout();
            this.splitContainer2.SuspendLayout();
            this.SuspendLayout();
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(13, 54);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(83, 12);
            this.label1.TabIndex = 2;
            this.label1.Text = "PPT模板路径：";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(13, 27);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(65, 12);
            this.label2.TabIndex = 2;
            this.label2.Text = "数据区域：";
            // 
            // button替换PPT关键字
            // 
            this.button替换PPT关键字.Location = new System.Drawing.Point(505, 22);
            this.button替换PPT关键字.Name = "button替换PPT关键字";
            this.button替换PPT关键字.Size = new System.Drawing.Size(106, 75);
            this.button替换PPT关键字.TabIndex = 3;
            this.button替换PPT关键字.Text = "替换关键字";
            this.button替换PPT关键字.UseVisualStyleBackColor = true;
            this.button替换PPT关键字.Click += new System.EventHandler(this.button替换PPT关键字_Click);
            // 
            // tabControl1
            // 
            this.tabControl1.Controls.Add(this.tabPage替换PPT);
            this.tabControl1.Controls.Add(this.tabPage转换为PDF);
            this.tabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabControl1.Location = new System.Drawing.Point(0, 0);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(630, 190);
            this.tabControl1.TabIndex = 4;
            this.tabControl1.SelectedIndexChanged += new System.EventHandler(this.tabControl1_SelectedIndexChanged);
            // 
            // tabPage替换PPT
            // 
            this.tabPage替换PPT.Controls.Add(this.textboxSavePath输出路径);
            this.tabPage替换PPT.Controls.Add(this.comboBoxPage处理页面);
            this.tabPage替换PPT.Controls.Add(this.ucFileSelectpptTemplatePath);
            this.tabPage替换PPT.Controls.Add(this.ucExcelRangeSelectData1);
            this.tabPage替换PPT.Controls.Add(this.checkBox复制前先把源文本删除);
            this.tabPage替换PPT.Controls.Add(this.checkBox从模板复制新文本到原PPT);
            this.tabPage替换PPT.Controls.Add(this.button替换PPT关键字);
            this.tabPage替换PPT.Controls.Add(this.label8);
            this.tabPage替换PPT.Controls.Add(this.label5);
            this.tabPage替换PPT.Controls.Add(this.label2);
            this.tabPage替换PPT.Controls.Add(this.label3);
            this.tabPage替换PPT.Controls.Add(this.label4);
            this.tabPage替换PPT.Controls.Add(this.label1);
            this.tabPage替换PPT.Location = new System.Drawing.Point(4, 22);
            this.tabPage替换PPT.Name = "tabPage替换PPT";
            this.tabPage替换PPT.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage替换PPT.Size = new System.Drawing.Size(622, 164);
            this.tabPage替换PPT.TabIndex = 0;
            this.tabPage替换PPT.Text = "根据数据表替换PPT";
            this.tabPage替换PPT.UseVisualStyleBackColor = true;
            // 
            // textboxSavePath输出路径
            // 
            this.textboxSavePath输出路径.Location = new System.Drawing.Point(102, 76);
            this.textboxSavePath输出路径.Name = "textboxSavePath输出路径";
            this.textboxSavePath输出路径.Size = new System.Drawing.Size(376, 21);
            this.textboxSavePath输出路径.TabIndex = 8;
            //
            // comboBoxPage处理页面
            //
            this.comboBoxPage处理页面.FormattingEnabled = true;
            this.comboBoxPage处理页面.Location = new System.Drawing.Point(102, 103);
            this.comboBoxPage处理页面.Name = "comboBoxPage处理页面";
            this.comboBoxPage处理页面.Size = new System.Drawing.Size(376, 20);
            this.comboBoxPage处理页面.TabIndex = 5;
            this.comboBoxPage处理页面.Text = "1,2,3,5,6";
            // 
            // ucFileSelectpptTemplatePath
            // 
            this.ucFileSelectpptTemplatePath.AutoFillLatestValue = true;
            this.ucFileSelectpptTemplatePath.DefaultFileExtension = "";
            this.ucFileSelectpptTemplatePath.FileFilter = "PPT (*.pptx)|*.pptx";
            this.ucFileSelectpptTemplatePath.Location = new System.Drawing.Point(102, 49);
            this.ucFileSelectpptTemplatePath.Name = "ucFileSelectpptTemplatePath";
            this.ucFileSelectpptTemplatePath.Size = new System.Drawing.Size(376, 21);
            this.ucFileSelectpptTemplatePath.TabIndex = 1;
            this.ucFileSelectpptTemplatePath.UseFolderBrowser = false;
            this.ucFileSelectpptTemplatePath.UseOpenFileDialog = true;
            // 
            // ucExcelRangeSelectData1
            // 
            this.ucExcelRangeSelectData1.EnableEnterThenSelect = false;
            this.ucExcelRangeSelectData1.HideParentForm = true;
            this.ucExcelRangeSelectData1.InputPromptText = "请选择：";
            this.ucExcelRangeSelectData1.Location = new System.Drawing.Point(102, 22);
            this.ucExcelRangeSelectData1.Name = "ucExcelRangeSelectData1";
            this.ucExcelRangeSelectData1.SelectedRange = null;
            this.ucExcelRangeSelectData1.Size = new System.Drawing.Size(376, 21);
            this.ucExcelRangeSelectData1.TabIndex = 0;
            // 
            // checkBox复制前先把源文本删除
            // 
            this.checkBox复制前先把源文本删除.AutoSize = true;
            this.checkBox复制前先把源文本删除.Location = new System.Drawing.Point(234, 137);
            this.checkBox复制前先把源文本删除.Name = "checkBox复制前先把源文本删除";
            this.checkBox复制前先把源文本删除.Size = new System.Drawing.Size(144, 16);
            this.checkBox复制前先把源文本删除.TabIndex = 6;
            this.checkBox复制前先把源文本删除.Text = "复制前先把源文本删除";
            this.checkBox复制前先把源文本删除.UseVisualStyleBackColor = true;
            this.checkBox复制前先把源文本删除.CheckedChanged += new System.EventHandler(this.checkBox从模板复制新文本到原PPT_CheckedChanged);
            // 
            // checkBox从模板复制新文本到原PPT
            // 
            this.checkBox从模板复制新文本到原PPT.AutoSize = true;
            this.checkBox从模板复制新文本到原PPT.Location = new System.Drawing.Point(15, 137);
            this.checkBox从模板复制新文本到原PPT.Name = "checkBox从模板复制新文本到原PPT";
            this.checkBox从模板复制新文本到原PPT.Size = new System.Drawing.Size(198, 16);
            this.checkBox从模板复制新文本到原PPT.TabIndex = 6;
            this.checkBox从模板复制新文本到原PPT.Text = "从模板复制新文本及表格到原PPT";
            this.checkBox从模板复制新文本到原PPT.UseVisualStyleBackColor = true;
            this.checkBox从模板复制新文本到原PPT.CheckedChanged += new System.EventHandler(this.checkBox从模板复制新文本到原PPT_CheckedChanged);
            // 
            // label8
            // 
            this.label8.AutoSize = true;
            this.label8.Location = new System.Drawing.Point(13, 108);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(77, 12);
            this.label8.TabIndex = 2;
            this.label8.Text = "只处理页面：";
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(484, 108);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(131, 12);
            this.label5.TabIndex = 2;
            this.label5.Text = "使用半角逗号,分隔页面";
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(13, 81);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(65, 12);
            this.label3.TabIndex = 2;
            this.label3.Text = "输出路径：";
            // 
            // label4
            // 
            this.label4.Location = new System.Drawing.Point(8, 348);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(598, 42);
            this.label4.TabIndex = 2;
            this.label4.Text = "注意：本功能只处理文本，不会处理表格。首列必须是文件全路径或者目标文件名；如果是修改现有PPT，可以不用输入模板路径，处理全部页面可以填入all\r\n";
            // 
            // tabPage转换为PDF
            // 
            this.tabPage转换为PDF.Controls.Add(this.button导入文件清单);
            this.tabPage转换为PDF.Controls.Add(this.ds原根路径_转换为PDF);
            this.tabPage转换为PDF.Controls.Add(this.ds输出路径_转换为PDF);
            this.tabPage转换为PDF.Controls.Add(this.label42);
            this.tabPage转换为PDF.Controls.Add(this.label29);
            this.tabPage转换为PDF.Controls.Add(this.button执行_转换为PDF);
            this.tabPage转换为PDF.Controls.Add(this.label30);
            this.tabPage转换为PDF.Controls.Add(this.ucERS文件路径_转换为PDF);
            this.tabPage转换为PDF.Location = new System.Drawing.Point(4, 22);
            this.tabPage转换为PDF.Name = "tabPage转换为PDF";
            this.tabPage转换为PDF.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage转换为PDF.Size = new System.Drawing.Size(622, 164);
            this.tabPage转换为PDF.TabIndex = 5;
            this.tabPage转换为PDF.Text = "转换为PDF";
            this.tabPage转换为PDF.UseVisualStyleBackColor = true;
            // 
            // button导入文件清单
            // 
            this.button导入文件清单.Location = new System.Drawing.Point(411, 23);
            this.button导入文件清单.Name = "button导入文件清单";
            this.button导入文件清单.Size = new System.Drawing.Size(108, 23);
            this.button导入文件清单.TabIndex = 20;
            this.button导入文件清单.Text = "导入文件清单";
            this.button导入文件清单.UseVisualStyleBackColor = true;
            this.button导入文件清单.Click += new System.EventHandler(this.button导入文件清单_Click);
            // 
            // ds原根路径_转换为PDF
            // 
            this.ds原根路径_转换为PDF.Location = new System.Drawing.Point(175, 94);
            this.ds原根路径_转换为PDF.Name = "ds原根路径_转换为PDF";
            this.ds原根路径_转换为PDF.Size = new System.Drawing.Size(344, 21);
            this.ds原根路径_转换为PDF.TabIndex = 19;
            // 
            // ds输出路径_转换为PDF
            // 
            this.ds输出路径_转换为PDF.Location = new System.Drawing.Point(139, 50);
            this.ds输出路径_转换为PDF.Name = "ds输出路径_转换为PDF";
            this.ds输出路径_转换为PDF.Size = new System.Drawing.Size(380, 21);
            this.ds输出路径_转换为PDF.TabIndex = 19;
            // 
            // label42
            // 
            this.label42.AutoSize = true;
            this.label42.Location = new System.Drawing.Point(20, 99);
            this.label42.Name = "label42";
            this.label42.Size = new System.Drawing.Size(149, 12);
            this.label42.TabIndex = 15;
            this.label42.Text = "保持目录结构，原根路径：";
            // 
            // label29
            // 
            this.label29.AutoSize = true;
            this.label29.Location = new System.Drawing.Point(20, 55);
            this.label29.Name = "label29";
            this.label29.Size = new System.Drawing.Size(65, 12);
            this.label29.TabIndex = 9;
            this.label29.Text = "输出路径：";
            // 
            // button执行_转换为PDF
            // 
            this.button执行_转换为PDF.Location = new System.Drawing.Point(525, 19);
            this.button执行_转换为PDF.Name = "button执行_转换为PDF";
            this.button执行_转换为PDF.Size = new System.Drawing.Size(85, 48);
            this.button执行_转换为PDF.TabIndex = 6;
            this.button执行_转换为PDF.Text = "执行";
            this.button执行_转换为PDF.UseVisualStyleBackColor = true;
            this.button执行_转换为PDF.Click += new System.EventHandler(this.button执行_转换为PDF_Click);
            // 
            // label30
            // 
            this.label30.AutoSize = true;
            this.label30.Location = new System.Drawing.Point(20, 28);
            this.label30.Name = "label30";
            this.label30.Size = new System.Drawing.Size(113, 12);
            this.label30.TabIndex = 2;
            this.label30.Text = "文件路径(单元格)：";
            // 
            // ucERS文件路径_转换为PDF
            // 
            this.ucERS文件路径_转换为PDF.EnableEnterThenSelect = false;
            this.ucERS文件路径_转换为PDF.HideParentForm = true;
            this.ucERS文件路径_转换为PDF.InputPromptText = "请选择：";
            this.ucERS文件路径_转换为PDF.Location = new System.Drawing.Point(139, 23);
            this.ucERS文件路径_转换为PDF.Name = "ucERS文件路径_转换为PDF";
            this.ucERS文件路径_转换为PDF.SelectedRange = null;
            this.ucERS文件路径_转换为PDF.Size = new System.Drawing.Size(266, 21);
            this.ucERS文件路径_转换为PDF.TabIndex = 0;
            // 
            // splitContainer1
            // 
            this.splitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer1.FixedPanel = System.Windows.Forms.FixedPanel.Panel1;
            this.splitContainer1.IsSplitterFixed = true;
            this.splitContainer1.Location = new System.Drawing.Point(0, 0);
            this.splitContainer1.Margin = new System.Windows.Forms.Padding(0);
            this.splitContainer1.Name = "splitContainer1";
            this.splitContainer1.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.tabControl1);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.splitContainer2);
            this.splitContainer1.Size = new System.Drawing.Size(630, 468);
            this.splitContainer1.SplitterDistance = 190;
            this.splitContainer1.SplitterWidth = 10;
            this.splitContainer1.TabIndex = 6;
            // 
            // splitContainer2
            // 
            this.splitContainer2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.splitContainer2.Location = new System.Drawing.Point(0, 0);
            this.splitContainer2.Margin = new System.Windows.Forms.Padding(0);
            this.splitContainer2.Name = "splitContainer2";
            this.splitContainer2.Orientation = System.Windows.Forms.Orientation.Horizontal;
            // 
            // splitContainer2.Panel1
            // 
            this.splitContainer2.Panel1.Controls.Add(this.textBoxError);
            this.splitContainer2.Panel1.Controls.Add(this.labelError);
            // 
            // splitContainer2.Panel2
            // 
            this.splitContainer2.Panel2.Controls.Add(this.textBoxProgress);
            this.splitContainer2.Panel2.Controls.Add(this.labelProgress);
            this.splitContainer2.Size = new System.Drawing.Size(630, 268);
            this.splitContainer2.SplitterDistance = 120;
            this.splitContainer2.TabIndex = 0;
            // 
            // textBoxError
            // 
            this.textBoxError.Dock = System.Windows.Forms.DockStyle.Fill;
            this.textBoxError.Location = new System.Drawing.Point(0, 0);
            this.textBoxError.Multiline = true;
            this.textBoxError.Name = "textBoxError";
            this.textBoxError.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBoxError.Size = new System.Drawing.Size(630, 120);
            this.textBoxError.TabIndex = 6;
            // 
            // labelError
            // 
            this.labelError.AutoSize = true;
            this.labelError.Location = new System.Drawing.Point(5, 5);
            this.labelError.Name = "labelError";
            this.labelError.Size = new System.Drawing.Size(65, 12);
            this.labelError.TabIndex = 5;
            this.labelError.Text = "错误信息：";
            // 
            // textBoxProgress
            // 
            this.textBoxProgress.Dock = System.Windows.Forms.DockStyle.Fill;
            this.textBoxProgress.Location = new System.Drawing.Point(0, 0);
            this.textBoxProgress.Multiline = true;
            this.textBoxProgress.Name = "textBoxProgress";
            this.textBoxProgress.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBoxProgress.Size = new System.Drawing.Size(630, 144);
            this.textBoxProgress.TabIndex = 6;
            // 
            // labelProgress
            // 
            this.labelProgress.AutoSize = true;
            this.labelProgress.Location = new System.Drawing.Point(5, 5);
            this.labelProgress.Name = "labelProgress";
            this.labelProgress.Size = new System.Drawing.Size(65, 12);
            this.labelProgress.TabIndex = 5;
            this.labelProgress.Text = "进度信息：";
            // 
            // frmPPTHelper
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(630, 468);
            this.Controls.Add(this.splitContainer1);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frmPPTHelper";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "PPT工具集";
            this.Load += new System.EventHandler(this.frmHelper_Load);
            this.tabControl1.ResumeLayout(false);
            this.tabPage替换PPT.ResumeLayout(false);
            this.tabPage替换PPT.PerformLayout();
            this.tabPage转换为PDF.ResumeLayout(false);
            this.tabPage转换为PDF.PerformLayout();
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).EndInit();
            this.splitContainer1.ResumeLayout(false);
            this.splitContainer2.Panel1.ResumeLayout(false);
            this.splitContainer2.Panel1.PerformLayout();
            this.splitContainer2.Panel2.ResumeLayout(false);
            this.splitContainer2.Panel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer2)).EndInit();
            this.splitContainer2.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private ETRangeSelectControl ucExcelRangeSelectData1;
        private ET.Controls.ETUcFileSelect ucFileSelectpptTemplatePath;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Button button替换PPT关键字;
        public System.Windows.Forms.TabControl tabControl1;
        public System.Windows.Forms.TabPage tabPage替换PPT;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.CheckBox checkBox从模板复制新文本到原PPT;
        private System.Windows.Forms.ComboBox comboBoxPage处理页面;
        private System.Windows.Forms.Label label8;
        private ET.Controls.HHUcDirectorySelect textboxSavePath输出路径;
        public System.Windows.Forms.TabPage tabPage转换为PDF;
        private System.Windows.Forms.Button button导入文件清单;
        private ET.Controls.HHUcDirectorySelect ds原根路径_转换为PDF;
        private ET.Controls.HHUcDirectorySelect ds输出路径_转换为PDF;
        private System.Windows.Forms.Label label42;
        private System.Windows.Forms.Label label29;
        private System.Windows.Forms.Button button执行_转换为PDF;
        private System.Windows.Forms.Label label30;
        private ETRangeSelectControl ucERS文件路径_转换为PDF;
        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.SplitContainer splitContainer2;
        private System.Windows.Forms.TextBox textBoxError;
        private System.Windows.Forms.Label labelError;
        private System.Windows.Forms.TextBox textBoxProgress;
        private System.Windows.Forms.Label labelProgress;
        private System.Windows.Forms.CheckBox checkBox复制前先把源文本删除;
        private System.Windows.Forms.Label label5;
    }
}