﻿using ET;
using HyExcelVsto.Extensions;
using Microsoft.Office.Interop.Excel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using Application = Microsoft.Office.Interop.Excel.Application;

namespace HyExcelVsto.Module.WX
{
    /// <summary>
    /// GPS经纬度格式化和坐标系转换窗体
    /// </summary>
    public partial class frm格式化经纬度 : Form
    {
        #region 变量

        /// <summary>
        /// Excel应用程序实例
        /// </summary>
        public Application XlApp;

        /// <summary>
        /// 存储处理后的GPS字符串列表
        /// </summary>
        private readonly List<string> _processedGpsResults = [];

        /// <summary>
        /// 存储待优化的单元格范围及其对应值
        /// </summary>
        private readonly Dictionary<Range, string> _cellRangeOutputs = [];

        #endregion 变量

        /// <summary>
        /// 初始化GPS格式化窗体
        /// </summary>
        public frm格式化经纬度()
        {
            InitializeComponent();
            XlApp = Globals.ThisAddIn.Application;
        }

        /// <summary>
        /// 双击列表项时执行GPS处理操作
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        private void listBox1_DoubleClicck(object sender, EventArgs e)
        {
            try
            {
                ProcessGpsCoordinates();
            }
            catch (ETException ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show(ex.Message, "GPS处理错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show("GPS处理过程中发生未知错误，请查看日志。", "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清空处理结果容器
        /// </summary>
        private void ClearResults()
        {
            _processedGpsResults.Clear();
            _cellRangeOutputs.Clear();
        }

        /// <summary>
        /// 处理GPS数据的主要方法
        /// </summary>
        private void ProcessGpsCoordinates()
        {
            Range selectedRange = ETExcelExtensions.GetSelectionRange();
            if (selectedRange == null)
            {
                throw new ETException("请先选择要处理的单元格范围。", "GPS数据处理");
            }

            ClearResults();
            textBoxLog.Text = "执行中...";
            ETExcelExtensions.SetAppFastMode();

            try
            {
                ValidateSelection(selectedRange);
                ProcessSelectedFunction(selectedRange);
            }
            catch (Exception ex)
            {
                throw new ETException("GPS数据处理失败", "GPS坐标处理", ex);
            }
            finally
            {
                ETExcelExtensions.SetAppNormalMode(true);
            }

            SetResultText();
        }

        /// <summary>
        /// 验证用户选择的单元格范围是否符合处理要求
        /// </summary>
        /// <param name="selectedRange">选中的Excel单元格范围</param>
        /// <exception cref="HyException">当选择范围不符合要求时抛出</exception>
        private void ValidateSelection(Range selectedRange)
        {
            if (selectedRange.Rows.Count > 50000)
            {
                throw new ETException("选择的行数不能超过5万行。", "数据范围验证");
            }

            if (selectedRange.Columns.Count > 20 || selectedRange.Columns.Count == 0)
            {
                throw new ETException("选择的列数不能超过20列且不能为空。", "数据范围验证");
            }
        }

        /// <summary>
        /// 根据用户选择的功能处理GPS数据
        /// </summary>
        /// <param name="selectedRange">选中的Excel单元格范围</param>
        /// <exception cref="HyException">当未选择功能或处理失败时抛出</exception>
        private void ProcessSelectedFunction(Range selectedRange)
        {
            string selectedFunction = listBox1.SelectedItem?.ToString();
            if (string.IsNullOrEmpty(selectedFunction))
            {
                throw new ETException("请选择要执行的功能。", "功能选择验证");
            }

            try
            {
                switch (selectedFunction)
                {
                    case "仅检查是否正确":
                        ExtractGpsCoordinates(selectedRange, false);
                        break;

                    case "仅提取经纬度":
                        ExtractGpsCoordinates(selectedRange, true);
                        break;

                    case "仅格式化处理":
                        FormatGpsCoordinates(selectedRange);
                        break;

                    default:
                        ConvertGpsCoordinates(selectedRange, selectedFunction);
                        break;
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                throw new ETException($"执行{selectedFunction}功能时发生错误", "GPS数据处理", ex);
            }
        }

        /// <summary>
        /// 从选中区域提取GPS经纬度数据
        /// </summary>
        /// <param name="selectedRange">选中的Excel单元格范围</param>
        private void ExtractGpsCoordinates(Range selectedRange, bool outputToCell)
        {
            foreach (Range currentRow in selectedRange.Rows)
            {
                string[] gpsCoordinates = ZnWireless.ExtractGpsAndAddress(currentRow);
                if (gpsCoordinates == null)
                {
                    HandleInvalidGps(currentRow);
                    continue;
                }

                if (!outputToCell) continue;

                string formattedOutput = checkBox合并经纬度.Checked
                    ? $"{gpsCoordinates[0]},{gpsCoordinates[1]}"
                    : $"{gpsCoordinates[0]}\t{gpsCoordinates[1]}";

                _processedGpsResults.Add(formattedOutput);
                if (checkBox合并经纬度.Checked)
                {
                    currentRow.Cells[1, 1].Value = formattedOutput;
                }
            }
        }

        /// <summary>
        /// 格式化GPS经纬度数据
        /// </summary>
        /// <param name="selectedRange">选中的Excel单元格范围</param>
        /// <exception cref="HyException">当选择的列数不符合要求时抛出</exception>
        private void FormatGpsCoordinates(Range selectedRange)
        {
            if (selectedRange.Columns.Count != 2 && !checkBox合并经纬度.Checked)
            {
                throw new ETException("格式化处理需要选择2列，或勾选合并经纬度选项。", "GPS格式化操作");
            }

            foreach (Range currentRow in selectedRange.Rows)
            {
                string[] gpsCoordinates = ZnWireless.ExtractGpsAndAddress(currentRow);
                if (gpsCoordinates == null)
                {
                    HandleInvalidGps(currentRow);
                    continue;
                }

                if (checkBox合并经纬度.Checked)
                {
                    string formattedOutput = $"{gpsCoordinates[0]},{gpsCoordinates[1]}";
                    currentRow.Cells[1, 1].Value = formattedOutput;
                    _processedGpsResults.Add(formattedOutput);
                }
                else
                {
                    currentRow.Cells[1, 1].Value = gpsCoordinates[0];
                    currentRow.Cells[1, 2].Value = gpsCoordinates[1];
                }
            }
        }

        /// <summary>
        /// 转换GPS坐标系
        /// </summary>
        /// <param name="selectedRange">选中的Excel单元格范围</param>
        /// <param name="conversionType">坐标转换类型</param>
        /// <exception cref="HyException">当选择的列数不符合要求或转换类型不支持时抛出</exception>
        private void ConvertGpsCoordinates(Range selectedRange, string conversionType)
        {
            if (selectedRange.Columns.Count != 2 && !checkBox合并经纬度.Checked)
            {
                throw new ETException("坐标转换需要选择2列，或勾选合并经纬度选项。", "坐标系转换操作");
            }

            ETGpsConvertUtil converter = new();
            List<double[]> coordinatesList = [];
            List<Range> processedRows = [];

            foreach (Range currentRow in selectedRange.Rows)
            {
                string[] gpsCoordinates = ZnWireless.ExtractGpsAndAddress(currentRow);
                if (gpsCoordinates == null)
                {
                    HandleInvalidGps(currentRow);
                    continue;
                }

                try
                {
                    double longitude = Convert.ToDouble(gpsCoordinates[0]);
                    double latitude = Convert.ToDouble(gpsCoordinates[1]);
                    coordinatesList.Add(new[] { longitude, latitude });
                    processedRows.Add(currentRow);
                }
                catch (FormatException ex)
                {
                    ETLogManager.Error(ex);
                    HandleInvalidGps(currentRow);
                }
            }

            double[][] coordinates = coordinatesList.ToArray();
            double[][] convertedCoordinates;

            switch (conversionType)
            {
                case "GCJ-02火星坐标系转WGS84坐标系":
                    convertedCoordinates = converter.gcj02towgs84(coordinates);
                    break;

                case "WGS84坐标系转百度坐标系":
                    convertedCoordinates = converter.wgs84tobd09(coordinates);
                    break;

                default:
                    throw new ETException("不支持的坐标转换类型。", "坐标系转换操作");
            }

            for (int i = 0; i < convertedCoordinates.Length; i++)
            {
                Range currentRow = processedRows[i];
                double[] convertedGps = convertedCoordinates[i];

                if (checkBox合并经纬度.Checked)
                {
                    string formattedOutput = $"{convertedGps[0]},{convertedGps[1]}";
                    currentRow.Cells[1, 1].Value = formattedOutput;
                    _processedGpsResults.Add(formattedOutput);
                }
                else
                {
                    currentRow.Cells[1, 1].Value = convertedGps[0];
                    currentRow.Cells[1, 2].Value = convertedGps[1];
                    _processedGpsResults.Add($"{convertedGps[0]}\t{convertedGps[1]}");
                }
            }
        }

        /// <summary>
        /// 处理无效的GPS数据
        /// </summary>
        /// <param name="cell">包含无效GPS数据的单元格</param>
        private void HandleInvalidGps(Range cell)
        {
            if (checkBox标色.Checked)
            {
                cell.Format条件格式警示色(EnumWarningColor.提醒);
            }
            _processedGpsResults.Add(string.Empty);
        }

        /// <summary>
        /// 设置结果文本并复制到剪贴板
        /// </summary>
        private void SetResultText()
        {
            StringBuilder resultText = new();

            if (_processedGpsResults.Count > 0)
            {
                resultText.AppendLine(string.Join(Environment.NewLine, _processedGpsResults));
                textBoxLog.Text = resultText.ToString();
                Clipboard.SetText(resultText.ToString());
            }
            else
            {
                textBoxLog.Text = "处理完成，但没有有效结果。";
            }
        }

        /// <summary>
        /// 窗体加载事件处理
        /// </summary>
        private void frm格式化经纬度_Load(object sender, EventArgs e)
        {
            try
            {
                listBox1.Items.Clear();
                listBox1.Items.AddRange(new object[]
                {
                    "仅检查是否正确",
                    "仅提取经纬度",
                    "仅格式化处理",
                    "GCJ-02火星坐标系转WGS84坐标系",
                    "WGS84坐标系转百度坐标系"
                });
            }
            catch (Exception ex)
            {
                ETLogManager.Error(ex);
                MessageBox.Show("窗体初始化失败。", "系统错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}