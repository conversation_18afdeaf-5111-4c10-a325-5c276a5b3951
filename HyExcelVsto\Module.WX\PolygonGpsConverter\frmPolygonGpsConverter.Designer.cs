namespace HyExcelVsto.Module.WX.PolygonGpsConverter
{
    partial class frmPolygonGpsConverter
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.lblDescription = new System.Windows.Forms.Label();
            this.txtRangeSelect = new System.Windows.Forms.TextBox();
            this.btnSelectRange = new System.Windows.Forms.Button();
            this.btnConvert = new System.Windows.Forms.Button();
            this.txtResult = new System.Windows.Forms.TextBox();
            this.chkConvertToBaidu = new System.Windows.Forms.CheckBox();
            this.groupBoxOutput = new System.Windows.Forms.GroupBox();
            this.groupBoxOutput.SuspendLayout();
            this.SuspendLayout();
            // 
            // lblDescription
            // 
            this.lblDescription.AutoSize = true;
            this.lblDescription.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblDescription.Location = new System.Drawing.Point(12, 15);
            this.lblDescription.Name = "lblDescription";
            this.lblDescription.Size = new System.Drawing.Size(480, 17);
            this.lblDescription.TabIndex = 0;
            this.lblDescription.Text = "多边形GPS坐标转换器 - 将Excel中的GPS坐标数据转换为多边形格式，支持多种坐标系统";
            // 
            // txtRangeSelect
            // 
            this.txtRangeSelect.Location = new System.Drawing.Point(16, 43);
            this.txtRangeSelect.Name = "txtRangeSelect";
            this.txtRangeSelect.ReadOnly = true;
            this.txtRangeSelect.Size = new System.Drawing.Size(300, 23);
            this.txtRangeSelect.TabIndex = 2;
            // 
            // btnSelectRange
            // 
            this.btnSelectRange.Location = new System.Drawing.Point(328, 42);
            this.btnSelectRange.Name = "btnSelectRange";
            this.btnSelectRange.Size = new System.Drawing.Size(100, 25);
            this.btnSelectRange.TabIndex = 3;
            this.btnSelectRange.Text = "选择范围";
            this.btnSelectRange.UseVisualStyleBackColor = true;
            this.btnSelectRange.Click += new System.EventHandler(this.btnSelectRange_Click);
            // 
            // btnConvert
            // 
            this.btnConvert.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(122)))), ((int)(((byte)(204)))));
            this.btnConvert.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnConvert.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnConvert.ForeColor = System.Drawing.Color.White;
            this.btnConvert.Location = new System.Drawing.Point(578, 42);
            this.btnConvert.Name = "btnConvert";
            this.btnConvert.Size = new System.Drawing.Size(120, 25);
            this.btnConvert.TabIndex = 5;
            this.btnConvert.Text = "开始转换";
            this.btnConvert.UseVisualStyleBackColor = false;
            this.btnConvert.Click += new System.EventHandler(this.btnConvert_Click);
            // 
            // txtResult
            // 
            this.txtResult.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txtResult.Location = new System.Drawing.Point(3, 19);
            this.txtResult.Multiline = true;
            this.txtResult.Name = "txtResult";
            this.txtResult.ReadOnly = true;
            this.txtResult.ScrollBars = System.Windows.Forms.ScrollBars.Both;
            this.txtResult.Size = new System.Drawing.Size(744, 204);
            this.txtResult.TabIndex = 6;
            this.txtResult.WordWrap = false;
            // 
            // chkConvertToBaidu
            // 
            this.chkConvertToBaidu.AutoSize = true;
            this.chkConvertToBaidu.Location = new System.Drawing.Point(448, 45);
            this.chkConvertToBaidu.Name = "chkConvertToBaidu";
            this.chkConvertToBaidu.Size = new System.Drawing.Size(123, 21);
            this.chkConvertToBaidu.TabIndex = 4;
            this.chkConvertToBaidu.Text = "转换为百度坐标系";
            this.chkConvertToBaidu.UseVisualStyleBackColor = true;
            // 
            // groupBoxOutput
            // 
            this.groupBoxOutput.Controls.Add(this.txtResult);
            this.groupBoxOutput.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.groupBoxOutput.Location = new System.Drawing.Point(15, 73);
            this.groupBoxOutput.Name = "groupBoxOutput";
            this.groupBoxOutput.Size = new System.Drawing.Size(750, 226);
            this.groupBoxOutput.TabIndex = 10;
            this.groupBoxOutput.TabStop = false;
            this.groupBoxOutput.Text = "转换结果";
            // 
            // frmPolygonGpsConverter
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 17F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(784, 318);
            this.Controls.Add(this.txtRangeSelect);
            this.Controls.Add(this.groupBoxOutput);
            this.Controls.Add(this.btnSelectRange);
            this.Controls.Add(this.chkConvertToBaidu);
            this.Controls.Add(this.btnConvert);
            this.Controls.Add(this.lblDescription);
            this.Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "frmPolygonGpsConverter";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "多边形GPS坐标转换器";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frmPolygonGpsConverter_FormClosing);
            this.Load += new System.EventHandler(this.frmPolygonGpsConverter_Load);
            this.groupBoxOutput.ResumeLayout(false);
            this.groupBoxOutput.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.Label lblDescription;
        private System.Windows.Forms.TextBox txtRangeSelect;
        private System.Windows.Forms.Button btnSelectRange;
        private System.Windows.Forms.Button btnConvert;
        private System.Windows.Forms.TextBox txtResult;
        private System.Windows.Forms.GroupBox groupBoxOutput;
        private System.Windows.Forms.CheckBox chkConvertToBaidu;
    }
}
