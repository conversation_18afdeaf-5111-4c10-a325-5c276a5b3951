﻿namespace HyExcelVsto.Module.WX
{
    partial class frm格式化经纬度
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.textBoxLog = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.checkBox标色 = new System.Windows.Forms.CheckBox();
            this.label1 = new System.Windows.Forms.Label();
            this.listBox1 = new System.Windows.Forms.ListBox();
            this.checkBox合并经纬度 = new System.Windows.Forms.CheckBox();
            this.SuspendLayout();
            // 
            // textBoxLog
            // 
            this.textBoxLog.Location = new System.Drawing.Point(6, 154);
            this.textBoxLog.Multiline = true;
            this.textBoxLog.Name = "textBoxLog";
            this.textBoxLog.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.textBoxLog.Size = new System.Drawing.Size(230, 167);
            this.textBoxLog.TabIndex = 7;
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(6, 136);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(35, 12);
            this.label2.TabIndex = 9;
            this.label2.Text = "日志:";
            // 
            // checkBox标色
            // 
            this.checkBox标色.AutoSize = true;
            this.checkBox标色.Checked = true;
            this.checkBox标色.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox标色.Location = new System.Drawing.Point(6, 4);
            this.checkBox标色.Name = "checkBox标色";
            this.checkBox标色.Size = new System.Drawing.Size(144, 16);
            this.checkBox标色.TabIndex = 5;
            this.checkBox标色.Text = "找不到经纬度的行标色";
            this.checkBox标色.UseVisualStyleBackColor = true;
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(6, 324);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(185, 36);
            this.label1.TabIndex = 12;
            this.label1.Text = "注：\r\n1.提取经纬度后自动复制到粘贴板\r\n2.隐藏行也会被处理";
            // 
            // listBox1
            // 
            this.listBox1.FormattingEnabled = true;
            this.listBox1.ItemHeight = 12;
            this.listBox1.Location = new System.Drawing.Point(6, 48);
            this.listBox1.Name = "listBox1";
            this.listBox1.Size = new System.Drawing.Size(230, 76);
            this.listBox1.TabIndex = 14;
            this.listBox1.DoubleClick += new System.EventHandler(this.listBox1_DoubleClicck);
            // 
            // checkBox合并经纬度
            // 
            this.checkBox合并经纬度.AutoSize = true;
            this.checkBox合并经纬度.Location = new System.Drawing.Point(6, 26);
            this.checkBox合并经纬度.Name = "checkBox合并经纬度";
            this.checkBox合并经纬度.Size = new System.Drawing.Size(108, 16);
            this.checkBox合并经纬度.TabIndex = 15;
            this.checkBox合并经纬度.Text = "逗号合并经纬度";
            this.checkBox合并经纬度.UseVisualStyleBackColor = true;
            // 
            // frm格式化经纬度
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(246, 368);
            this.Controls.Add(this.checkBox合并经纬度);
            this.Controls.Add(this.listBox1);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.textBoxLog);
            this.Controls.Add(this.checkBox标色);
            this.Controls.Add(this.label2);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedToolWindow;
            this.Name = "frm格式化经纬度";
            this.ShowIcon = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "格式化经纬度";
            this.Load += new System.EventHandler(this.frm格式化经纬度_Load);
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.TextBox textBoxLog;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.CheckBox checkBox标色;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.ListBox listBox1;
        private System.Windows.Forms.CheckBox checkBox合并经纬度;
    }
}