{"RootPath": "D:\\HyDevelop\\HyHelper\\HyHelper\\HyExcelVsto", "ProjectFileName": "HyExcelVsto.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Extensions\\ZnWireless.cs"}, {"SourceFile": "GlobalSettings.cs"}, {"SourceFile": "Extensions\\ucExcelCandidateList.cs"}, {"SourceFile": "Extensions\\ucExcelCandidateList.Designer.cs"}, {"SourceFile": "Extensions\\Interfaces\\IExcelEventReceiver.cs"}, {"SourceFile": "Extensions\\Interfaces\\IExcelMessageReceiver.cs"}, {"SourceFile": "HyLicenseManager.cs"}, {"SourceFile": "HyRibbon.cs"}, {"SourceFile": "HyRibbon.Designer.cs"}, {"SourceFile": "HyUIPermissionManager.cs"}, {"SourceFile": "Module.AI\\frmAIv2.cs"}, {"SourceFile": "Module.AI\\frmAIv2.Designer.cs"}, {"SourceFile": "Module.Common\\AssemblyLoadTracker.cs"}, {"SourceFile": "Module.Common\\ExcelFileManagerHelper.cs"}, {"SourceFile": "Module.Common\\ExcelFileRecord.cs"}, {"SourceFile": "Module.Common\\ExcelFileRecordManager.cs"}, {"SourceFile": "Module.Common\\FormManager.cs"}, {"SourceFile": "Module.Common\\frmAssemblyTracker.cs"}, {"SourceFile": "Module.Common\\frmCrosshairOverlayForm.cs"}, {"SourceFile": "Module.Common\\frmCrosshairOverlayForm.Designer.cs"}, {"SourceFile": "Module.Common\\frmDropdownInputForm.cs"}, {"SourceFile": "Module.Common\\frmDropdownInputForm.Designer.cs"}, {"SourceFile": "Module.Common\\frmExcelFileManager.cs"}, {"SourceFile": "Module.Common\\frmExcelFileManager.Designer.cs"}, {"SourceFile": "Module.Common\\frmLongStringDisplayForm.cs"}, {"SourceFile": "Module.Common\\frmLongStringDisplayForm.Designer.cs"}, {"SourceFile": "Module.Common\\frmTopForm.cs"}, {"SourceFile": "Module.Common\\frmTopForm.Designer.cs"}, {"SourceFile": "Module.Common\\frmVisioHelper.cs"}, {"SourceFile": "Module.Common\\frmVisioHelper.Designer.cs"}, {"SourceFile": "Module.Common\\frmVisioHelperFunctions3.cs"}, {"SourceFile": "Module.Common\\frmVisioHelperFunctions2.cs"}, {"SourceFile": "Module.Common\\frmWordHelper.cs"}, {"SourceFile": "Module.Common\\frmWordHelper.Designer.cs"}, {"SourceFile": "Module.Common\\frmWPS打开.cs"}, {"SourceFile": "Module.Common\\frmWPS打开.Designer.cs"}, {"SourceFile": "Module.Common\\frm向下填充.cs"}, {"SourceFile": "Module.Common\\frm向下填充.Designer.cs"}, {"SourceFile": "Module.Common\\frm批量查找.cs"}, {"SourceFile": "Module.Common\\frm批量查找.Designer.cs"}, {"SourceFile": "Module.Common\\frm文本复制粘贴辅助框.cs"}, {"SourceFile": "Module.Common\\frm文本复制粘贴辅助框.Designer.cs"}, {"SourceFile": "Module.Common\\frm自动脚本2.cs"}, {"SourceFile": "Module.Common\\frm设置标签及下拉候选项.cs"}, {"SourceFile": "Module.Common\\frm设置标签及下拉候选项.Designer.cs"}, {"SourceFile": "Module.Common\\frmVisioHelperFunctions1.cs"}, {"SourceFile": "Module.Common\\HyFunctions2.cs"}, {"SourceFile": "Extensions\\MenuManager.cs"}, {"SourceFile": "Extensions\\TaskPaneManager.cs"}, {"SourceFile": "Module.Common\\OfficeImageIdExportHelper.cs"}, {"SourceFile": "Module.Dialog\\dlgBox模板.cs"}, {"SourceFile": "Module.Dialog\\dlgBox模板.Designer.cs"}, {"SourceFile": "Module.Common\\frm复制及合并.cs"}, {"SourceFile": "Module.Common\\frm复制及合并.Designer.cs"}, {"SourceFile": "Module.Common\\frm备份及发送.cs"}, {"SourceFile": "Module.Common\\frm备份及发送.Designer.cs"}, {"SourceFile": "Module.Common\\frm字符处理.cs"}, {"SourceFile": "Module.Common\\frm字符处理.Designer.cs"}, {"SourceFile": "Module.Common\\frm文件操作.cs"}, {"SourceFile": "Module.Common\\frm文件操作.Designer.cs"}, {"SourceFile": "Module.Common\\frm前后添加删除字符.cs"}, {"SourceFile": "Module.Common\\frm前后添加删除字符.Designer.cs"}, {"SourceFile": "Module.Common\\frmPPTHelper.cs"}, {"SourceFile": "Module.Common\\frmPPTHelper.Designer.cs"}, {"SourceFile": "Module.Wenzi\\ScheduleConverter.cs"}, {"SourceFile": "Module.Wenzi\\Utils\\Logging\\ETLoggerAdapter.cs"}, {"SourceFile": "Module.Wenzi\\Utils\\Logging\\ILogger.cs"}, {"SourceFile": "Module.Wenzi\\Utils\\Logging\\TextBoxLogger.cs"}, {"SourceFile": "Module.WX\\51Helper\\Dx51HelpAPI.cs"}, {"SourceFile": "Module.WX\\51Helper\\Dx51HelpBase.cs"}, {"SourceFile": "Module.WX\\51Helper\\Dx51HelpOther.cs"}, {"SourceFile": "Module.WX\\51Helper\\Dx51HelpTask1.cs"}, {"SourceFile": "Module.WX\\51Helper\\Dx51HelpTask2.cs"}, {"SourceFile": "Module.WX\\51Helper\\Dx51TaskProcessor.cs"}, {"SourceFile": "Module.WX\\51Helper\\frm51Helper.cs"}, {"SourceFile": "Module.WX\\51Helper\\frm51Helper.Designer.cs"}, {"SourceFile": "Module.WX\\51Helper\\frm51Helper2.cs"}, {"SourceFile": "Module.WX\\AngleExtractor\\AngleExtractorForm.cs"}, {"SourceFile": "Module.WX\\AngleExtractor\\AngleExtractorForm.Designer.cs"}, {"SourceFile": "Module.WX\\AngleExtractor\\AngleExtractorHelper.cs"}, {"SourceFile": "Module.WX\\frmGPS生成图层.cs"}, {"SourceFile": "Module.WX\\frmGPS生成图层.Designer.cs"}, {"SourceFile": "Module.Common\\frm填表同步数据.cs"}, {"SourceFile": "Module.Common\\frm填表同步数据.Designer.cs"}, {"SourceFile": "Module.WX\\frm查找站点.cs"}, {"SourceFile": "Module.WX\\frm查找站点.Designer.cs"}, {"SourceFile": "Module.WX\\KmlConverter_Jy\\frmKmlConverter.cs"}, {"SourceFile": "Module.WX\\KmlConverter_Jy\\frmKmlConverter.Designer.cs"}, {"SourceFile": "Module.WX\\OrderKmlGenerator\\Config\\OrderKmlConfig.cs"}, {"SourceFile": "Module.WX\\OrderKmlGenerator\\Core\\OrderDataExtractor.cs"}, {"SourceFile": "Module.WX\\OrderKmlGenerator\\Core\\OrderDataProcessor.cs"}, {"SourceFile": "Module.WX\\OrderKmlGenerator\\Core\\OrderKmlExceptionHandler.cs"}, {"SourceFile": "Module.WX\\OrderKmlGenerator\\Core\\OrderKmlGenerator.cs"}, {"SourceFile": "Module.WX\\OrderKmlGenerator\\Core\\OrderKmlLogger.cs"}, {"SourceFile": "Module.WX\\OrderKmlGenerator\\Models\\OrderKmlPoint.cs"}, {"SourceFile": "Module.WX\\OrderKmlGenerator\\Models\\OrderKmlResult.cs"}, {"SourceFile": "Module.WX\\OrderKmlGenerator\\Models\\OrderStationData.cs"}, {"SourceFile": "Module.WX\\OrderKmlGenerator\\OrderKmlGeneratorHelper.cs"}, {"SourceFile": "Module.WX\\OrderKmlGenerator\\UI\\OrderKmlGeneratorForm.cs"}, {"SourceFile": "Module.WX\\OrderKmlGenerator\\UI\\OrderKmlGeneratorForm.Designer.cs"}, {"SourceFile": "Module.WX\\PolygonGpsConverter\\frmPolygonGpsConverter.cs"}, {"SourceFile": "Module.WX\\PolygonGpsConverter\\frmPolygonGpsConverter.Designer.cs"}, {"SourceFile": "Module.WX\\PolygonGpsConverter\\PolygonGpsConverterHelper.cs"}, {"SourceFile": "Module.WX\\StationConverter\\Core\\StationDataConverter.cs"}, {"SourceFile": "Module.WX\\StationConverter\\Core\\StationGroupProcessor.cs"}, {"SourceFile": "Module.WX\\StationConverter\\Models\\FrequencyBandConfig.cs"}, {"SourceFile": "Module.WX\\StationConverter\\Models\\LogicalStation.cs"}, {"SourceFile": "Module.WX\\StationConverter\\Models\\PhysicalStation.cs"}, {"SourceFile": "Module.WX\\StationConverter\\StationConverterForm.cs"}, {"SourceFile": "Module.WX\\StationConverter\\StationConverterForm.Designer.cs"}, {"SourceFile": "Module.WX\\StationConverter\\StationConverterHelper.cs"}, {"SourceFile": "Module.WX\\StationDataProcessor\\Core\\ErrorHandlingConfig.cs"}, {"SourceFile": "Module.WX\\StationDataProcessor\\Core\\ExcelDataAccess.cs"}, {"SourceFile": "Module.WX\\StationDataProcessor\\Core\\Interfaces\\IDataAnalyzer.cs"}, {"SourceFile": "Module.WX\\StationDataProcessor\\Core\\Interfaces\\IDataProcessor.cs"}, {"SourceFile": "Module.WX\\StationDataProcessor\\Core\\Interfaces\\IExcelDataAccess.cs"}, {"SourceFile": "Module.WX\\StationDataProcessor\\Core\\StationDataAnalyzer.cs"}, {"SourceFile": "Module.WX\\StationDataProcessor\\Core\\StationDataProcessor.cs"}, {"SourceFile": "Module.WX\\StationDataProcessor\\Core\\StationDataProcessorConfig.cs"}, {"SourceFile": "Module.WX\\StationDataProcessor\\StationDataProcessorForm.cs"}, {"SourceFile": "Module.WX\\StationDataProcessor\\StationDataProcessorForm.Designer.cs"}, {"SourceFile": "Module.WX\\StationDataProcessor\\StationDataProcessorHelper.cs"}, {"SourceFile": "Module.WX\\TowerAccountProcessor_JY\\TowerAccountProcessorForm.cs"}, {"SourceFile": "Module.WX\\TowerAccountProcessor_JY\\TowerAccountProcessorForm.Designer.cs"}, {"SourceFile": "Module.WX\\TowerAccountProcessor_JY\\TowerAccountProcessorHelper.cs"}, {"SourceFile": "Module.Common\\HyFunctions.cs"}, {"SourceFile": "Module.Common\\frm合规检查.cs"}, {"SourceFile": "Module.Common\\frm合规检查.Designer.cs"}, {"SourceFile": "Module.Common\\frm工作表管理.cs"}, {"SourceFile": "Module.Common\\frm工作表管理.Designer.cs"}, {"SourceFile": "Module.Common\\frm自动脚本.cs"}, {"SourceFile": "Module.Common\\frm自动脚本.Designer.cs"}, {"SourceFile": "Module.Common\\frm设置页眉页脚.cs"}, {"SourceFile": "Module.Common\\frm设置页眉页脚.Designer.cs"}, {"SourceFile": "Module.WX\\frm格式化经纬度.cs"}, {"SourceFile": "Module.WX\\frm格式化经纬度.Designer.cs"}, {"SourceFile": "Module.Wenzi\\Constants\\AttendanceConstants.cs"}, {"SourceFile": "Module.Wenzi\\Controls\\ProgressDialog.cs"}, {"SourceFile": "Module.Wenzi\\frm考勤.cs"}, {"SourceFile": "Module.Wenzi\\frm考勤.Designer.cs"}, {"SourceFile": "Module.Wenzi\\Judges\\BaseJudge.cs"}, {"SourceFile": "Module.Wenzi\\Judges\\ExceptionRecordJudge.cs"}, {"SourceFile": "Module.Wenzi\\Judges\\LeaveJudge.cs"}, {"SourceFile": "Module.Wenzi\\Judges\\NightShiftJudge.cs"}, {"SourceFile": "Module.Wenzi\\Judges\\NormalShiftJudge.cs"}, {"SourceFile": "Module.Wenzi\\Models\\AttendanceModels.cs"}, {"SourceFile": "Module.Wenzi\\Models\\AttendanceRuleConfig.cs"}, {"SourceFile": "Module.Wenzi\\Models\\ConfigurationModels.cs"}, {"SourceFile": "Module.Wenzi\\Models\\ExcelSheetConfig.cs"}, {"SourceFile": "Module.Wenzi\\Services\\AttendanceConfigService.cs"}, {"SourceFile": "Module.Wenzi\\Services\\AttendanceService.cs"}, {"SourceFile": "Module.Wenzi\\Services\\Base\\DataValidatorBase.cs"}, {"SourceFile": "Module.Wenzi\\Services\\Base\\ExcelServiceBase.cs"}, {"SourceFile": "Module.Wenzi\\Services\\EmployeeValidationService.cs"}, {"SourceFile": "Module.Wenzi\\Services\\ExcelService.cs"}, {"SourceFile": "Module.Wenzi\\Services\\ExcelTemplateService.cs"}, {"SourceFile": "Module.Wenzi\\Services\\IAttendanceConfigService.cs"}, {"SourceFile": "Module.Wenzi\\Utils\\ConfigManager.cs"}, {"SourceFile": "Module.Wenzi\\Utils\\Exceptions\\AttendanceException.cs"}, {"SourceFile": "Module.Wenzi\\Utils\\Exceptions\\DataValidationException.cs"}, {"SourceFile": "Module.Wenzi\\Utils\\Exceptions\\ExcelOperationException.cs"}, {"SourceFile": "Module.Wenzi\\Utils\\Extensions\\StringExtensions.cs"}, {"SourceFile": "Module.Wenzi\\Utils\\PerformanceMonitor.cs"}, {"SourceFile": "Module.Wenzi\\Utils\\ProgressReporter.cs"}, {"SourceFile": "Module.Wenzi\\Utils\\ShiftNameFormatter.cs"}, {"SourceFile": "Module.Wenzi\\Utils\\TimeHelper.cs"}, {"SourceFile": "Module.Wenzi\\Utils\\TimeSpanExtensions.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "Properties\\Settings.Designer.cs"}, {"SourceFile": "Ribbon.cs"}, {"SourceFile": "HyExcelVstoSettings1.Designer.cs"}, {"SourceFile": "ThisAddIn.cs"}, {"SourceFile": "ThisAddIn.Designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\AngleSharp.1.3.0\\lib\\net472\\AngleSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Castle.Core.5.2.1\\lib\\net462\\Castle.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\bin\\Debug\\ExtensionsTools.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\HyDevelop\\HyHelper\\HyHelper\\ExtensionsTools\\bin\\Debug\\ExtensionsTools.dll"}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\GeoAPI.CoordinateSystems.1.7.5\\lib\\net45\\GeoAPI.CoordinateSystems.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\GeoAPI.Core.1.7.5\\lib\\net45\\GeoAPI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Microsoft.Bcl.AsyncInterfaces.9.0.6\\lib\\net462\\Microsoft.Bcl.AsyncInterfaces.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.5\\lib\\net462\\Microsoft.Extensions.DependencyInjection.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Microsoft.Extensions.DependencyInjection.9.0.5\\lib\\net462\\Microsoft.Extensions.DependencyInjection.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Microsoft.Extensions.Logging.Abstractions.9.0.5\\lib\\net462\\Microsoft.Extensions.Logging.Abstractions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\Visual Studio Tools for Office\\PIA\\Office15\\Microsoft.Office.Interop.Excel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\Visual Studio Tools for Office\\PIA\\Office15\\Microsoft.Office.Interop.PowerPoint.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\Visual Studio Tools for Office\\PIA\\Office15\\Microsoft.Office.Interop.Visio.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\Visual Studio Tools for Office\\PIA\\Office15\\Microsoft.Office.Interop.Word.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\ReferenceAssemblies\\v4.0\\Microsoft.Office.Tools.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\ReferenceAssemblies\\v4.0\\Microsoft.Office.Tools.Common.v4.0.Utilities.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\ReferenceAssemblies\\v4.0\\Microsoft.Office.Tools.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\ReferenceAssemblies\\v4.0\\Microsoft.Office.Tools.Excel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\ReferenceAssemblies\\v4.0\\Microsoft.Office.Tools.v4.0.Framework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Microsoft.VisualBasic.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\Common7\\IDE\\ReferenceAssemblies\\v4.0\\Microsoft.VisualStudio.Tools.Applications.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Microsoft.Web.Infrastructure.2.0.0\\lib\\net40\\Microsoft.Web.Infrastructure.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Microsoft.Win32.Registry.5.0.0\\lib\\net461\\Microsoft.Win32.Registry.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\NetTopologySuite.2.6.0\\lib\\netstandard2.0\\NetTopologySuite.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Newtonsoft.Json.13.0.3\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\Visual Studio Tools for Office\\PIA\\Office15\\Office.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\ServiceStack.Client.8.8.0\\lib\\net472\\ServiceStack.Client.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\ServiceStack.Common.8.8.0\\lib\\net472\\ServiceStack.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\ServiceStack.8.8.0\\lib\\net472\\ServiceStack.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\ServiceStack.Interfaces.8.8.0\\lib\\net472\\ServiceStack.Interfaces.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\ServiceStack.Text.8.8.0\\lib\\net472\\ServiceStack.Text.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Microsoft.NET\\Primary Interop Assemblies\\stdole.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Buffers.4.6.1\\lib\\net462\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Collections.Immutable.9.0.5\\lib\\net462\\System.Collections.Immutable.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.ComponentModel.Composition.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.ComponentModel.DataAnnotations.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.OracleClient.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Diagnostics.DiagnosticSource.9.0.5\\lib\\net462\\System.Diagnostics.DiagnosticSource.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Drawing.Common.9.0.5\\lib\\net462\\System.Drawing.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.IO.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.IO.Pipelines.9.0.6\\lib\\net462\\System.IO.Pipelines.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Management.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Memory.Data.9.0.6\\lib\\net462\\System.Memory.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Memory.4.6.3\\lib\\net462\\System.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Net.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Net.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Numerics.Vectors.4.6.1\\lib\\net462\\System.Numerics.Vectors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Runtime.CompilerServices.Unsafe.6.1.2\\lib\\net462\\System.Runtime.CompilerServices.Unsafe.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Runtime.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Security.AccessControl.6.0.1\\lib\\net461\\System.Security.AccessControl.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Security.Cryptography.Algorithms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Security.Cryptography.Encoding.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Security.Cryptography.Primitives.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\System.Security.Cryptography.X509Certificates.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Security.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Security.Principal.Windows.5.0.0\\lib\\net461\\System.Security.Principal.Windows.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.ServiceModel.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.ServiceProcess.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Text.Encoding.CodePages.9.0.5\\lib\\net462\\System.Text.Encoding.CodePages.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Text.Encodings.Web.9.0.6\\lib\\net462\\System.Text.Encodings.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Text.Json.9.0.6\\lib\\net462\\System.Text.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Threading.Channels.9.0.5\\lib\\net462\\System.Threading.Channels.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\System.Threading.Tasks.Extensions.4.6.3\\lib\\net462\\System.Threading.Tasks.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Transactions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Microsoft.AspNet.WebPages.3.3.0\\lib\\net45\\System.Web.Helpers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Microsoft.AspNet.Mvc.5.3.0\\lib\\net45\\System.Web.Mvc.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Microsoft.AspNet.Razor.3.3.0\\lib\\net45\\System.Web.Razor.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Microsoft.AspNet.WebPages.3.3.0\\lib\\net45\\System.Web.WebPages.Deployment.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Microsoft.AspNet.WebPages.3.3.0\\lib\\net45\\System.Web.WebPages.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\HyDevelop\\HyHelper\\HyHelper\\packages\\Microsoft.AspNet.WebPages.3.3.0\\lib\\net45\\System.Web.WebPages.Razor.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\WindowsBase.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\HyDevelop\\HyHelper\\HyHelper\\HyExcelVsto\\bin\\Debug\\HyExcelVsto.dll", "OutputItemRelativePath": "HyExcelVsto.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}